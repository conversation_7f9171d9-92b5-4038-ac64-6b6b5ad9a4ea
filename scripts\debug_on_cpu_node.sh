#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 申请CPU节点并调试ESM-2训练脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🖥️  申请CPU节点并调试ESM-2训练脚本"
echo "平台: s20223050931@202.205.88.251"
echo "=========================================="

# 检查是否在SLURM环境中
if [[ -z "$SLURM_JOB_ID" ]]; then
    print_info "当前不在SLURM作业中，将申请交互式CPU节点"
    
    # 显示申请参数
    echo "📋 CPU节点申请参数:"
    echo "  - 分区: cpu"
    echo "  - CPU核心: 16核"
    echo "  - 内存: 64GB"
    echo "  - 时间: 4小时（调试用）"
    echo "  - 模式: 交互式"
    echo ""
    
    read -p "是否确认申请CPU节点？(y/N): " confirm
    if [[ $confirm != [yY] ]]; then
        echo "取消申请"
        exit 0
    fi
    
    print_info "正在申请交互式CPU节点..."
    print_warning "请耐心等待节点分配，这可能需要几分钟时间"
    
    # 申请交互式节点并在其上运行此脚本
    srun --partition=cpu \
         --cpus-per-task=16 \
         --mem=64G \
         --time=04:00:00 \
         --pty \
         bash -c "cd $(pwd) && $0 --on-compute-node"
    
    exit 0
fi

# 如果已经在计算节点上
if [[ "$1" == "--on-compute-node" ]]; then
    print_success "🎉 成功分配到CPU节点!"
    echo "=========================================="
    echo "📊 节点信息:"
    echo "  - 节点名: $SLURMD_NODENAME"
    echo "  - 作业ID: $SLURM_JOB_ID"
    echo "  - CPU核心: $SLURM_CPUS_PER_TASK"
    echo "  - 内存: ${SLURM_MEM_PER_NODE}MB"
    echo "  - 开始时间: $(date)"
    echo "=========================================="
    
    # 1. 激活conda环境
    print_info "激活conda环境..."
    source ~/miniconda3/etc/profile.d/conda.sh || source ~/anaconda3/etc/profile.d/conda.sh
    
    if conda env list | grep -q "amp_esm2"; then
        conda activate amp_esm2
        print_success "amp_esm2环境激活成功"
    else
        print_error "amp_esm2环境不存在，请先运行环境配置脚本"
        echo "运行: ./scripts/setup_hpc_environment.sh"
        exit 1
    fi
    
    # 2. 设置环境变量
    print_info "配置环境变量..."
    export OMP_NUM_THREADS=$SLURM_CPUS_PER_TASK
    export MKL_NUM_THREADS=$SLURM_CPUS_PER_TASK
    export NUMEXPR_NUM_THREADS=$SLURM_CPUS_PER_TASK
    export OPENBLAS_NUM_THREADS=$SLURM_CPUS_PER_TASK
    export TOKENIZERS_PARALLELISM=false
    
    print_success "环境变量配置完成"
    echo "  - OMP_NUM_THREADS: $OMP_NUM_THREADS"
    echo "  - MKL_NUM_THREADS: $MKL_NUM_THREADS"
    
    # 3. 验证环境
    print_info "验证Python环境..."
    echo "Python版本: $(python --version)"
    echo "工作目录: $(pwd)"
    echo "Conda环境: $CONDA_DEFAULT_ENV"
    
    # 验证关键包
    print_info "验证关键依赖包..."
    python -c "
import sys
print(f'Python路径: {sys.executable}')

# 验证核心包
packages_to_check = {
    'torch': 'PyTorch',
    'pandas': 'Pandas', 
    'numpy': 'NumPy',
    'Bio': 'BioPython',
    'sklearn': 'Scikit-learn',
    'matplotlib': 'Matplotlib',
    'tqdm': 'TQDM'
}

failed_packages = []
for package, name in packages_to_check.items():
    try:
        __import__(package)
        print(f'✅ {name}: 导入成功')
    except ImportError as e:
        print(f'❌ {name}: 导入失败 - {e}')
        failed_packages.append(name)

# 验证PyTorch
try:
    import torch
    print(f'PyTorch版本: {torch.__version__}')
    print(f'CPU线程数: {torch.get_num_threads()}')
    print(f'CUDA可用: {torch.cuda.is_available()}')
except Exception as e:
    print(f'PyTorch验证失败: {e}')
    failed_packages.append('PyTorch')

# 验证ESM
try:
    import esm
    print('✅ ESM模型库: 导入成功')
    
    # 测试小模型加载
    print('🧪 测试ESM模型加载...')
    model, alphabet = esm.pretrained.esm2_t6_8M_UR50D()
    print('✅ ESM-2 8M模型加载成功')
    del model, alphabet  # 释放内存
    
except ImportError as e:
    print(f'❌ ESM模型库: 导入失败 - {e}')
    failed_packages.append('ESM')
except Exception as e:
    print(f'⚠️  ESM模型加载测试失败: {e}')

if failed_packages:
    print(f'\\n⚠️  以下包可能有问题: {failed_packages}')
    sys.exit(1)
else:
    print('\\n🎉 所有核心包验证通过!')
"
    
    if [ $? -ne 0 ]; then
        print_error "环境验证失败，请检查依赖安装"
        exit 1
    fi
    
    # 4. 创建调试目录
    print_info "创建调试目录..."
    mkdir -p debug_session/$(date +%Y%m%d_%H%M%S)
    DEBUG_DIR="debug_session/$(date +%Y%m%d_%H%M%S)"
    cd $DEBUG_DIR
    
    # 复制必要文件到调试目录
    cp ../../ESM2_AMP_Training_Complete.ipynb ./
    cp ../../hpc_notebook_setup.ipynb ./
    cp -r ../../scripts ./
    cp -r ../../configs ./
    
    print_success "调试环境准备完成"
    echo "调试目录: $(pwd)"
    
    # 5. 启动交互式调试会话
    print_info "启动交互式调试会话..."
    echo "=========================================="
    echo "🧪 调试环境已准备就绪！"
    echo "=========================================="
    echo ""
    echo "📋 可用的调试命令:"
    echo "1. python scripts/notebook_hpc_setup.py  # 测试环境配置"
    echo "2. python scripts/verify_esm_compatibility.py  # 验证ESM兼容性"
    echo "3. jupyter lab --ip=0.0.0.0 --port=8888 --no-browser  # 启动Jupyter"
    echo "4. python -c \"exec(open('scripts/notebook_hpc_setup.py').read())\"  # 快速环境测试"
    echo ""
    echo "📊 系统资源:"
    echo "  - CPU核心: $SLURM_CPUS_PER_TASK"
    echo "  - 可用内存: $(free -h | grep '^Mem:' | awk '{print $7}')"
    echo "  - 磁盘空间: $(df -h . | tail -1 | awk '{print $4}')"
    echo ""
    echo "💡 调试提示:"
    echo "  - 使用 'exit' 退出调试会话"
    echo "  - 调试文件保存在: $DEBUG_DIR"
    echo "  - 按 Ctrl+C 可以中断当前操作"
    echo ""
    echo "🚀 开始调试您的ESM-2训练脚本吧！"
    echo "=========================================="
    
    # 启动交互式shell
    exec bash --rcfile <(echo "
        export PS1='[ESM2-Debug] \u@\h:\w\$ '
        echo '🧬 ESM-2调试环境已激活'
        echo '当前目录: $(pwd)'
        echo '可用命令: python, jupyter, conda等'
        echo '输入 exit 退出调试会话'
    ")
    
else
    print_error "脚本执行错误，请检查参数"
    exit 1
fi
