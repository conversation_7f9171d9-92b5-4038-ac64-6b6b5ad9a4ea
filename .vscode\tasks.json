{"version": "2.0.0", "tasks": [{"label": "🚀 启动高算平台Jupyter", "type": "shell", "command": "./scripts/vscode_hpc_integration.sh", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "detail": "在高算平台申请CPU节点并启动Jupyter Lab"}, {"label": "🔧 配置高算环境", "type": "shell", "command": "./scripts/setup_hpc_environment.sh", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "detail": "配置高算平台conda环境和依赖"}, {"label": "🧪 验证ESM兼容性", "type": "shell", "command": "python scripts/verify_esm_compatibility.py", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "detail": "验证ESM模型与官网方法的兼容性"}, {"label": "📊 检查作业状态", "type": "shell", "command": "squeue -u s20223050931", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "查看当前SLURM作业状态"}, {"label": "🧹 清理环境", "type": "shell", "command": "conda clean --all && rm -rf temp/* && rm -rf logs/*.out logs/*.err", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "detail": "清理conda缓存和临时文件"}]}