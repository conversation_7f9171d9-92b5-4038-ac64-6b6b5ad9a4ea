#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 高算平台连接测试脚本

echo "🔍 高算平台连接测试"
echo "平台: s20223050931@**************"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

# 1. 测试网络连通性
print_info "测试网络连通性..."
if ping -c 3 ************** >/dev/null 2>&1; then
    print_success "网络连通性正常"
else
    print_error "网络连接失败，请检查网络设置"
    exit 1
fi

# 2. 测试SSH连接
print_info "测试SSH连接..."
if ssh -o ConnectTimeout=10 -o BatchMode=yes s20223050931@************** "echo 'SSH连接成功'" 2>/dev/null; then
    print_success "SSH连接成功（已配置免密登录）"
elif ssh -o ConnectTimeout=10 s20223050931@************** "echo 'SSH连接成功'" 2>/dev/null; then
    print_success "SSH连接成功（需要密码验证）"
else
    print_warning "SSH连接失败，请检查用户名和密码"
fi

# 3. 测试SSH配置
print_info "检查SSH配置..."
if [ -f ~/.ssh/config ]; then
    if grep -q "**************" ~/.ssh/config; then
        print_success "SSH配置文件存在且包含高算平台配置"
    else
        print_warning "SSH配置文件存在但未包含高算平台配置"
        echo "请运行: ./configure_vscode_hpc.sh"
    fi
else
    print_warning "SSH配置文件不存在"
    echo "请运行: ./configure_vscode_hpc.sh"
fi

# 4. 测试别名连接
if grep -q "Host hpc-main" ~/.ssh/config 2>/dev/null; then
    print_info "测试SSH别名连接..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes hpc-main "echo 'SSH别名连接成功'" 2>/dev/null; then
        print_success "SSH别名连接成功"
    else
        print_warning "SSH别名连接需要密码验证"
    fi
fi

# 5. 检查VSCode配置
print_info "检查VSCode配置..."
if [ -d ".vscode" ]; then
    print_success "VSCode配置目录存在"
    
    if [ -f ".vscode/settings.json" ]; then
        print_success "VSCode设置文件存在"
    else
        print_warning "VSCode设置文件缺失"
    fi
    
    if [ -f ".vscode/tasks.json" ]; then
        print_success "VSCode任务配置存在"
    else
        print_warning "VSCode任务配置缺失"
    fi
else
    print_warning "VSCode配置目录不存在"
    echo "请运行: ./configure_vscode_hpc.sh"
fi

# 6. 检查项目脚本
print_info "检查项目脚本..."
scripts=(
    "scripts/vscode_hpc_integration.sh"
    "scripts/setup_hpc_environment.sh"
    "scripts/run_jupyter_hpc.sh"
)

for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        if [ -x "$script" ]; then
            print_success "脚本存在且可执行: $script"
        else
            print_warning "脚本存在但不可执行: $script"
            echo "运行: chmod +x $script"
        fi
    else
        print_error "脚本缺失: $script"
    fi
done

echo ""
echo "=========================================="
print_info "测试总结"
echo "=========================================="

echo "📋 如果所有测试都通过，您可以："
echo "1. 打开VSCode"
echo "2. 安装Remote-SSH扩展"
echo "3. 连接到 hpc-main 或 s20223050931@**************"
echo "4. 运行 ./scripts/vscode_hpc_integration.sh"
echo ""
echo "🔧 如果有测试失败，请："
echo "1. 运行 ./configure_vscode_hpc.sh 进行配置"
echo "2. 检查网络连接和SSH设置"
echo "3. 确保用户名和密码正确"
echo ""
echo "📖 详细指南: docs/vscode_hpc_guide.md"
