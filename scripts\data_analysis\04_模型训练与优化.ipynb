{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 抗菌肽分类模型训练与优化 (第4-5周)\n", "\n", "**作者**: ZK  \n", "**邮箱**: <EMAIL>  \n", "**日期**: 2025-07-20  \n", "**描述**: 基于ESM-2特征训练抗菌肽分类模型，包含超参数优化和模型验证\n", "\n", "## 目标\n", "1. 构建分类模型架构\n", "2. 实现训练流程\n", "3. 超参数调优\n", "4. 模型性能评估"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境设置和依赖导入"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler\n", "from torch.cuda.amp import autocast, GradScaler\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.model_selection import StratifiedKFold\n", "from sklearn.metrics import (\n", "    accuracy_score, precision_score, recall_score, f1_score,\n", "    roc_auc_score, average_precision_score, matthews_corrcoef,\n", "    confusion_matrix, classification_report, roc_curve\n", ")\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from tqdm import tqdm\n", "import pickle\n", "import json\n", "import os\n", "from datetime import datetime\n", "import warnings\n", "import optuna\n", "from collections import defaultdict\n", "import wandb  # 可选：用于实验跟踪\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置随机种子\n", "torch.manual_seed(42)\n", "np.random.seed(42)\n", "\n", "print(\"✅ 库导入完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 路径配置\n", "PROJECT_ROOT = os.path.abspath(os.path.join(os.getcwd(), '..', '..'))\n", "DATA_ANALYSIS_READY_DIR = os.path.join(PROJECT_ROOT, 'data', 'analysis_ready')\n", "MODEL_OUTPUT_DIR = os.path.join(PROJECT_ROOT, 'models')\n", "RESULTS_DIR = os.path.join(PROJECT_ROOT, 'results')\n", "\n", "# 创建输出目录\n", "os.makedirs(MODEL_OUTPUT_DIR, exist_ok=True)\n", "os.makedirs(RESULTS_DIR, exist_ok=True)\n", "\n", "# 设备配置\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")\n", "\n", "if torch.cuda.is_available():\n", "    print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 数据加载和预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载特征和标签数据\n", "def load_processed_data():\n", "    \"\"\"加载处理好的特征和标签\"\"\"\n", "    try:\n", "        # 查找最新的特征文件\n", "        feature_files = [f for f in os.listdir(DATA_ANALYSIS_READY_DIR) if f.startswith('features_') and f.endswith('.npy')]\n", "        if not feature_files:\n", "            raise FileNotFoundError(\"未找到特征文件，请先运行特征提取notebook\")\n", "        \n", "        latest_feature_file = sorted(feature_files)[-1]\n", "        feature_path = os.path.join(DATA_ANALYSIS_READY_DIR, latest_feature_file)\n", "        \n", "        # 加载特征\n", "        features = np.load(feature_path)\n", "        print(f\"加载特征文件: {latest_feature_file}\")\n", "        print(f\"特征形状: {features.shape}\")\n", "        \n", "        # 查找对应的标签文件\n", "        label_file = latest_feature_file.replace('features_', 'labels_').replace('.npy', '.npy')\n", "        label_path = os.path.join(DATA_ANALYSIS_READY_DIR, label_file)\n", "        \n", "        if os.path.exists(label_path):\n", "            labels = np.load(label_path)\n", "            print(f\"加载标签文件: {label_file}\")\n", "        else:\n", "            raise FileNotFoundError(f\"未找到对应的标签文件: {label_file}\")\n", "        \n", "        # 查找元数据文件\n", "        metadata_file = latest_feature_file.replace('features_', 'metadata_').replace('.npy', '.json')\n", "        metadata_path = os.path.join(DATA_ANALYSIS_READY_DIR, metadata_file)\n", "        \n", "        metadata = None\n", "        if os.path.exists(metadata_path):\n", "            with open(metadata_path, 'r', encoding='utf-8') as f:\n", "                metadata = json.load(f)\n", "            print(f\"加载元数据文件: {metadata_file}\")\n", "        \n", "        return features, labels, metadata\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 数据加载失败: {e}\")\n", "        raise\n", "\n", "# 加载数据\n", "features, labels, metadata = load_processed_data()\n", "\n", "print(f\"\\n数据统计:\")\n", "print(f\"样本总数: {len(features)}\")\n", "print(f\"特征维度: {features.shape[1]}\")\n", "print(f\"正样本: {np.sum(labels)} ({np.mean(labels)*100:.1f}%)\")\n", "print(f\"负样本: {len(labels) - np.sum(labels)} ({(1-np.mean(labels))*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 数据集类定义"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class AMPDataset(Dataset):\n", "    \"\"\"抗菌肽数据集类\"\"\"\n", "    \n", "    def __init__(self, features, labels, transform=None):\n", "        self.features = torch.FloatTensor(features)\n", "        self.labels = torch.LongTensor(labels)\n", "        self.transform = transform\n", "        \n", "    def __len__(self):\n", "        return len(self.features)\n", "    \n", "    def __getitem__(self, idx):\n", "        feature = self.features[idx]\n", "        label = self.labels[idx]\n", "        \n", "        if self.transform:\n", "            feature = self.transform(feature)\n", "            \n", "        return feature, label\n", "    \n", "    def get_class_weights(self):\n", "        \"\"\"计算类别权重用于处理不平衡数据\"\"\"\n", "        class_counts = np.bincount(self.labels.numpy())\n", "        total_samples = len(self.labels)\n", "        weights = total_samples / (len(class_counts) * class_counts)\n", "        return torch.FloatTensor(weights)\n", "\n", "print(\"✅ AMPDataset类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 模型架构定义"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class AMPClassifier(nn.Module):\n", "    \"\"\"抗菌肽分类器\"\"\"\n", "    \n", "    def __init__(self, input_dim, hidden_dims=[512, 256, 128], dropout=0.3, num_classes=2):\n", "        super(AMPClassifier, self).__init__()\n", "        \n", "        self.input_dim = input_dim\n", "        self.hidden_dims = hidden_dims\n", "        self.dropout = dropout\n", "        self.num_classes = num_classes\n", "        \n", "        # 构建网络层\n", "        layers = []\n", "        prev_dim = input_dim\n", "        \n", "        for hidden_dim in hidden_dims:\n", "            layers.extend([\n", "                nn.Linear(prev_dim, hidden_dim),\n", "                nn.BatchNorm1d(hidden_dim),\n", "                nn.ReLU(inplace=True),\n", "                nn.Dropout(dropout)\n", "            ])\n", "            prev_dim = hidden_dim\n", "        \n", "        # 输出层\n", "        layers.append(nn.Linear(prev_dim, num_classes))\n", "        \n", "        self.classifier = nn.Sequential(*layers)\n", "        \n", "        # 初始化权重\n", "        self._initialize_weights()\n", "    \n", "    def _initialize_weights(self):\n", "        \"\"\"初始化网络权重\"\"\"\n", "        for m in self.modules():\n", "            if isinstance(m, nn.Linear):\n", "                nn.init.xavier_uniform_(m.weight)\n", "                if m.bias is not None:\n", "                    nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, nn.BatchNorm1d):\n", "                nn.init.constant_(m.weight, 1)\n", "                nn.init.constant_(m.bias, 0)\n", "    \n", "    def forward(self, x):\n", "        return self.classifier(x)\n", "    \n", "    def get_model_info(self):\n", "        \"\"\"获取模型信息\"\"\"\n", "        total_params = sum(p.numel() for p in self.parameters())\n", "        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)\n", "        \n", "        return {\n", "            'input_dim': self.input_dim,\n", "            'hidden_dims': self.hidden_dims,\n", "            'dropout': self.dropout,\n", "            'num_classes': self.num_classes,\n", "            'total_params': total_params,\n", "            'trainable_params': trainable_params\n", "        }\n", "\n", "print(\"✅ AMPClassifier类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 训练器类定义"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class AMPTrainer:\n", "    \"\"\"抗菌肽分类器训练器\"\"\"\n", "    \n", "    def __init__(self, model, device='cuda', use_amp=True):\n", "        self.model = model.to(device)\n", "        self.device = device\n", "        self.use_amp = use_amp and torch.cuda.is_available()\n", "        self.scaler = GradScaler() if self.use_amp else None\n", "        \n", "        # 训练历史\n", "        self.history = defaultdict(list)\n", "        \n", "    def train_epoch(self, dataloader, optimizer, criterion, epoch):\n", "        \"\"\"训练一个epoch\"\"\"\n", "        self.model.train()\n", "        total_loss = 0\n", "        all_preds = []\n", "        all_labels = []\n", "        \n", "        pbar = tqdm(dataloader, desc=f'Epoch {epoch+1} [Train]')\n", "        \n", "        for batch_features, batch_labels in pbar:\n", "            batch_features = batch_features.to(self.device)\n", "            batch_labels = batch_labels.to(self.device)\n", "            \n", "            optimizer.zero_grad()\n", "            \n", "            if self.use_amp:\n", "                with autocast():\n", "                    outputs = self.model(batch_features)\n", "                    loss = criterion(outputs, batch_labels)\n", "                \n", "                self.scaler.scale(loss).backward()\n", "                self.scaler.unscale_(optimizer)\n", "                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)\n", "                self.scaler.step(optimizer)\n", "                self.scaler.update()\n", "            else:\n", "                outputs = self.model(batch_features)\n", "                loss = criterion(outputs, batch_labels)\n", "                loss.backward()\n", "                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)\n", "                optimizer.step()\n", "            \n", "            total_loss += loss.item()\n", "            \n", "            # 收集预测结果\n", "            with torch.no_grad():\n", "                probs = torch.softmax(outputs, dim=1)\n", "                preds = torch.argmax(probs, dim=1)\n", "                all_preds.extend(preds.cpu().numpy())\n", "                all_labels.extend(batch_labels.cpu().numpy())\n", "            \n", "            # 更新进度条\n", "            pbar.set_postfix({'loss': f'{loss.item():.4f}'})\n", "        \n", "        avg_loss = total_loss / len(dataloader)\n", "        accuracy = accuracy_score(all_labels, all_preds)\n", "        \n", "        return avg_loss, accuracy\n", "    \n", "    def validate_epoch(self, dataloader, criterion, epoch):\n", "        \"\"\"验证一个epoch\"\"\"\n", "        self.model.eval()\n", "        total_loss = 0\n", "        all_preds = []\n", "        all_probs = []\n", "        all_labels = []\n", "        \n", "        with torch.no_grad():\n", "            pbar = tqdm(dataloader, desc=f'Epoch {epoch+1} [Val]')\n", "            \n", "            for batch_features, batch_labels in pbar:\n", "                batch_features = batch_features.to(self.device)\n", "                batch_labels = batch_labels.to(self.device)\n", "                \n", "                if self.use_amp:\n", "                    with autocast():\n", "                        outputs = self.model(batch_features)\n", "                        loss = criterion(outputs, batch_labels)\n", "                else:\n", "                    outputs = self.model(batch_features)\n", "                    loss = criterion(outputs, batch_labels)\n", "                \n", "                total_loss += loss.item()\n", "                \n", "                # 收集预测结果\n", "                probs = torch.softmax(outputs, dim=1)\n", "                preds = torch.argmax(probs, dim=1)\n", "                \n", "                all_preds.extend(preds.cpu().numpy())\n", "                all_probs.extend(probs[:, 1].cpu().numpy())  # 正类概率\n", "                all_labels.extend(batch_labels.cpu().numpy())\n", "                \n", "                pbar.set_postfix({'loss': f'{loss.item():.4f}'})\n", "        \n", "        avg_loss = total_loss / len(dataloader)\n", "        \n", "        # 计算评估指标\n", "        metrics = self.calculate_metrics(all_labels, all_preds, all_probs)\n", "        \n", "        return avg_loss, metrics\n", "    \n", "    def calculate_metrics(self, y_true, y_pred, y_prob):\n", "        \"\"\"计算评估指标\"\"\"\n", "        metrics = {\n", "            'accuracy': accuracy_score(y_true, y_pred),\n", "            'precision': precision_score(y_true, y_pred, average='binary'),\n", "            'recall': recall_score(y_true, y_pred, average='binary'),\n", "            'f1': f1_score(y_true, y_pred, average='binary'),\n", "            'auroc': roc_auc_score(y_true, y_prob),\n", "            'auprc': average_precision_score(y_true, y_prob),\n", "            'mcc': matthews_corrcoef(y_true, y_pred)\n", "        }\n", "        \n", "        return metrics\n", "    \n", "    def save_checkpoint(self, epoch, optimizer, scheduler, best_metric, filepath):\n", "        \"\"\"保存检查点\"\"\"\n", "        checkpoint = {\n", "            'epoch': epoch,\n", "            'model_state_dict': self.model.state_dict(),\n", "            'optimizer_state_dict': optimizer.state_dict(),\n", "            'scheduler_state_dict': scheduler.state_dict() if scheduler else None,\n", "            'best_metric': best_metric,\n", "            'history': dict(self.history),\n", "            'model_config': self.model.get_model_info()\n", "        }\n", "        \n", "        torch.save(checkpoint, filepath)\n", "        print(f\"✅ 检查点已保存: {filepath}\")\n", "\n", "print(\"✅ AMPTrainer类定义完成\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}