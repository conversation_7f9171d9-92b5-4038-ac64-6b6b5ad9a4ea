#!/bin/bash
# 抗菌肽项目环境配置脚本
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-20
# 描述: 自动创建和配置conda环境，安装所有必要依赖

echo "🚀 开始配置抗菌肽分类项目环境..."

# 检查conda是否已安装
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: 未检测到conda，请先安装Miniconda或Anaconda"
    exit 1
fi

# 设置环境名称
ENV_NAME="amp_esm2"
PYTHON_VERSION="3.9"

echo "📦 创建conda环境: $ENV_NAME (Python $PYTHON_VERSION)"

# 创建新的conda环境
conda create -n $ENV_NAME python=$PYTHON_VERSION -y

# 激活环境
echo "⚡ 激活环境..."
eval "$(conda shell.bash hook)"
conda activate $ENV_NAME

# 检查CUDA环境
echo "🔍 检查CUDA环境..."
if command -v nvidia-smi &> /dev/null; then
    echo "✅ 检测到NVIDIA GPU，将安装CUDA版本的PyTorch"
    # 安装CUDA版本的PyTorch
    conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
else
    echo "⚠️  未检测到CUDA，将安装CPU版本的PyTorch"
    # 安装CPU版本的PyTorch
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
fi

# 安装其他依赖
echo "📚 安装其他依赖包..."
pip install -r requirements.txt

# 验证安装
echo "🔬 验证安装..."
python -c "
import torch
import pandas as pd
import numpy as np
from Bio import SeqIO
print('✅ 核心包导入成功')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA版本: {torch.version.cuda}')
    print(f'GPU数量: {torch.cuda.device_count()}')
"

echo ""
echo "🎉 环境配置完成！"
echo ""
echo "📋 使用说明:"
echo "1. 激活环境: conda activate $ENV_NAME"
echo "2. 运行数据下载: cd scripts/data_download && jupyter notebook 01_数据收集与下载.ipynb"
echo "3. 退出环境: conda deactivate"
echo ""
echo "💡 提示: 建议在VSCode/Cursor中选择正确的Python解释器路径" 