# 抗革兰氏阴性菌高活性抗菌肽数据收集计划 (第二部分)

---

## 📖 阶段4: 文献挖掘与高质量数据获取 (第13-18天)

### 4.1 顶级期刊文献挖掘策略

#### 目标期刊列表 (按优先级)

**Tier 1期刊** (影响因子 >10):
- Nature Biotechnology (IF: 68.2)
- Nature Microbiology (IF: 28.3) 
- Science Translational Medicine (IF: 17.1)
- Cell Host & Microbe (IF: 31.3)
- Nature Medicine (IF: 87.2)

**Tier 2期刊** (影响因子 5-10):
- Journal of Medicinal Chemistry (IF: 8.0)
- Antimicrobial Agents and Chemotherapy (IF: 5.9)
- ACS Infectious Diseases (IF: 7.7)
- mBio (IF: 6.4)
- PNAS (IF: 11.1)

**Tier 3期刊** (影响因子 2-5):
- Peptides (IF: 3.0)
- Biochimica et Biophysica Acta (IF: 4.3)
- International Journal of Antimicrobial Agents (IF: 4.9)
- Journal of Antimicrobial Chemotherapy (IF: 6.1)
- Applied and Environmental Microbiology (IF: 4.4)

#### 文献搜索关键词策略

**核心关键词组合**:
```
组合1: "antimicrobial peptide" AND "gram-negative" AND "MIC"
组合2: "antibacterial peptide" AND "E. coli" AND "activity"  
组合3: "AMP" AND "Pseudomonas" AND "therapeutic"
组合4: "peptide antibiotic" AND "gram-negative bacteria"
组合5: "antimicrobial peptide" AND "multidrug resistant" AND "gram-negative"
```

**高级搜索策略**:
```
PubMed搜索式:
("antimicrobial peptides"[MeSH Terms] OR "antimicrobial peptide"[All Fields]) 
AND ("gram-negative bacteria"[MeSH Terms] OR "gram-negative"[All Fields])
AND ("minimum inhibitory concentration"[All Fields] OR "MIC"[All Fields])
AND ("2020/01/01"[PDAT] : "2025/12/31"[PDAT])
```

#### 每日文献挖掘计划

**第13天: Nature/Science系列**
- 目标期刊: Nature Biotechnology, Nature Microbiology
- 预期文献: 10-15篇
- 预期序列: 50-80个
- 重点: 突破性发现和新机制

**第14天: 医学化学期刊**
- 目标期刊: J Med Chem, ACS Infect Dis
- 预期文献: 15-20篇  
- 预期序列: 80-120个
- 重点: 构效关系和优化序列

**第15天: 微生物学期刊**
- 目标期刊: AAC, mBio, JAC
- 预期文献: 20-25篇
- 预期序列: 100-150个
- 重点: 临床相关性和耐药性

**第16天: 专业肽类期刊**
- 目标期刊: Peptides, BBA
- 预期文献: 15-20篇
- 预期序列: 60-100个
- 重点: 肽类设计和修饰

**第17-18天: 综合整理和验证**
- 文献数据整合
- 重复序列识别
- 活性数据验证

### 4.2 文献数据提取标准流程

#### 文献筛选标准

**纳入标准**:
- [ ] 包含抗革兰氏阴性菌肽序列
- [ ] 提供明确的MIC值
- [ ] 实验方法描述清楚
- [ ] 同行评议期刊发表
- [ ] 2015年后发表 (优先2020年后)

**排除标准**:
- [ ] 仅有预测活性，无实验验证
- [ ] 序列信息不完整
- [ ] 实验条件不明确
- [ ] 非英文文献 (除非特别重要)

#### 数据提取模板

**文献基本信息**:
```
Paper_ID: LIT_XXX
PMID: XXXXXXXX
DOI: 10.XXXX/XXXXX
Title: [完整标题]
Journal: [期刊名称]
Year: XXXX
Impact_Factor: XX.X
```

**肽序列信息**:
```
Peptide_Name: [文献中的名称]
Sequence: [氨基酸序列]
Length: XX
Modifications: [修饰信息，如有]
```

**活性数据**:
```
Target_Organism: [具体菌株]
MIC_Value: XX.X
MIC_Unit: μg/mL
Experimental_Method: [实验方法]
Culture_Medium: [培养基]
Incubation_Conditions: [培养条件]
```

**质量评估**:
```
Data_Quality: [High/Medium/Low]
Experimental_Rigor: [评分1-5]
Clinical_Relevance: [评分1-5]
Novelty: [评分1-5]
```

### 4.3 高质量数据验证流程

#### 三级验证体系

**一级验证 (自动化)**:
```python
def primary_validation(record):
    checks = {
        'sequence_valid': validate_amino_acid_sequence(record['Sequence']),
        'mic_reasonable': 0.1 <= record['MIC_Value'] <= 1000,
        'length_appropriate': 5 <= record['Length'] <= 100,
        'organism_confirmed': record['Target_Organism'] in GRAM_NEGATIVE_LIST
    }
    return checks
```

**二级验证 (专家审核)**:
- 序列-活性关系合理性
- 实验设计科学性
- 数据一致性检查
- 文献质量评估

**三级验证 (交叉验证)**:
- 多个数据源对比
- 独立实验结果验证
- 同类肽活性比较
- 异常值调查

---

## 🔄 阶段5: 数据整合、去重与质量控制 (第19-22天)

### 5.1 数据整合策略

#### 数据源优先级

**优先级排序**:
1. **文献直接提取** (最高优先级)
2. **DBAASP验证数据** 
3. **CAMPR4补充数据**
4. **APD历史数据**
5. **其他数据库**

#### 整合流程

**第19天: 数据汇总**
```python
# 数据整合脚本框架
def integrate_data_sources():
    # 1. 加载各数据源
    literature_data = load_literature_data()
    dbaasp_data = load_dbaasp_data()
    campr4_data = load_campr4_data()
    apd_data = load_apd_data()
    
    # 2. 标准化格式
    standardized_data = []
    for source, data in [('LIT', literature_data), 
                        ('DBAASP', dbaasp_data),
                        ('CAMPR4', campr4_data), 
                        ('APD', apd_data)]:
        standardized_data.extend(standardize_format(data, source))
    
    # 3. 初步去重
    unique_data = remove_exact_duplicates(standardized_data)
    
    return unique_data
```

### 5.2 序列去重算法

#### 多层次去重策略

**第一层: 完全相同序列**
```python
def remove_exact_duplicates(data):
    seen_sequences = set()
    unique_data = []
    
    for record in data:
        seq = record['Sequence'].upper()
        if seq not in seen_sequences:
            seen_sequences.add(seq)
            unique_data.append(record)
        else:
            # 保留质量更高的记录
            existing_record = find_existing_record(unique_data, seq)
            if record['Quality_Score'] > existing_record['Quality_Score']:
                unique_data.remove(existing_record)
                unique_data.append(record)
    
    return unique_data
```

**第二层: 高相似序列 (≥95%)**
```python
def remove_high_similarity_duplicates(data, threshold=0.95):
    from Bio import pairwise2
    
    filtered_data = []
    
    for i, record in enumerate(data):
        is_duplicate = False
        
        for j, existing in enumerate(filtered_data):
            similarity = calculate_sequence_similarity(
                record['Sequence'], 
                existing['Sequence']
            )
            
            if similarity >= threshold:
                # 保留质量更高的
                if record['Quality_Score'] > existing['Quality_Score']:
                    filtered_data[j] = record
                is_duplicate = True
                break
        
        if not is_duplicate:
            filtered_data.append(record)
    
    return filtered_data
```

**第三层: 中等相似序列 (80-95%)**
- 人工审核决定是否保留
- 考虑活性差异
- 评估序列功能域

### 5.3 质量控制检查点

#### 最终质量控制标准

**数据完整性检查**:
```python
def final_quality_check(record):
    required_fields = [
        'Peptide_ID', 'Sequence', 'Target_Organism', 
        'MIC_Value', 'MIC_Unit', 'Reference_PMID'
    ]
    
    completeness_score = sum([
        bool(record.get(field)) for field in required_fields
    ]) / len(required_fields)
    
    return completeness_score >= 0.9  # 90%完整性要求
```

**科学合理性检查**:
- MIC值与序列长度的合理性
- 活性与序列特征的一致性  
- 实验条件的标准化程度
- 文献来源的可靠性

**统计质量检查**:
- 序列长度分布合理性
- MIC值分布正态性
- 菌株分布均衡性
- 数据源分布多样性

---

## 📊 阶段6: 数据集构建与验证 (第23-25天)

### 6.1 最终数据集构建

#### 数据集结构设计

**主数据集** (gram_negative_amps.csv):
- 包含所有高质量序列
- 完整的元数据信息
- 标准化的格式

**训练集** (training_set.csv):
- 70%的数据用于模型训练
- 平衡的菌株分布
- 多样的活性范围

**验证集** (validation_set.csv):
- 15%的数据用于模型验证
- 独立的文献来源
- 代表性的序列特征

**测试集** (test_set.csv):
- 15%的数据用于最终测试
- 最高质量的数据
- 临床相关的序列

#### 数据集划分策略

```python
def split_dataset(data, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15):
    # 按质量评分排序
    data_sorted = sorted(data, key=lambda x: x['Quality_Score'], reverse=True)
    
    # 分层抽样，确保各菌株均匀分布
    train_set, val_set, test_set = stratified_split(
        data_sorted, 
        stratify_by='Target_Organism',
        ratios=[train_ratio, val_ratio, test_ratio]
    )
    
    return train_set, val_set, test_set
```

### 6.2 数据集验证

#### 质量验证指标

**数据质量指标**:
- 平均质量评分 ≥ 8.0
- 数据完整性 ≥ 95%
- 文献可追溯性 = 100%
- 重复率 ≤ 5%

**数据分布指标**:
- 序列长度: 正态分布，均值25±10 AA
- MIC值: 对数正态分布，中位数≤16 μg/mL
- 菌株分布: E.coli≥40%, P.aeruginosa≥25%, A.baumannii≥15%

**数据多样性指标**:
- 氨基酸组成多样性
- 序列模式多样性
- 来源文献多样性
- 实验条件多样性

#### 最终验证报告模板

```
抗革兰氏阴性菌高活性抗菌肽数据集验证报告
=============================================

数据集概览:
- 总序列数: XXX个
- 高活性序列(≤16 μg/mL): XXX个 (XX%)
- 平均质量评分: X.X
- 数据完整性: XX%

菌株分布:
- E. coli: XXX个 (XX%)
- P. aeruginosa: XXX个 (XX%)  
- A. baumannii: XXX个 (XX%)
- 其他: XXX个 (XX%)

活性分布:
- 超高活性(≤4 μg/mL): XXX个 (XX%)
- 高活性(4-16 μg/mL): XXX个 (XX%)
- 中等活性(16-32 μg/mL): XXX个 (XX%)

序列特征:
- 平均长度: XX.X ± XX.X AA
- 长度范围: XX-XX AA
- 氨基酸多样性指数: X.XX

数据来源:
- 文献直接提取: XXX个 (XX%)
- DBAASP: XXX个 (XX%)
- CAMPR4: XXX个 (XX%)
- APD: XXX个 (XX%)

质量控制:
- 去重前总数: XXX个
- 去重后保留: XXX个 (XX%)
- 质量筛选通过率: XX%

验证结论:
[ ] 数据集满足机器学习训练要求
[ ] 数据质量达到预期标准
[ ] 数据分布合理均衡
[ ] 可以开始模型训练

建议:
1. XXX
2. XXX
3. XXX
```

---

## 📅 时间安排与里程碑

### 详细时间表

| 阶段 | 时间 | 主要任务 | 预期产出 | 质量检查点 |
|------|------|----------|----------|------------|
| **阶段1** | 第1-2天 | 环境搭建 | 模板和标准 | 工具就绪 |
| **阶段2** | 第3-8天 | DBAASP收集 | 600-800个序列 | 质量评分≥8.0 |
| **阶段3** | 第9-12天 | 其他数据库 | 300-500个序列 | 数据完整性≥90% |
| **阶段4** | 第13-18天 | 文献挖掘 | 400-600个序列 | 文献质量Tier1-2 |
| **阶段5** | 第19-22天 | 整合去重 | 1000-1500个序列 | 重复率≤5% |
| **阶段6** | 第23-25天 | 验证构建 | 最终数据集 | 全面质量验证 |

### 关键里程碑

- **第8天**: DBAASP数据收集完成
- **第12天**: 主要数据库收集完成  
- **第18天**: 文献挖掘完成
- **第22天**: 数据整合去重完成
- **第25天**: 最终数据集构建完成

---

## 🎯 成功标准与验收条件

### 最终交付物

1. **高质量数据集** (1,000-2,000个序列)
2. **详细的数据文档** (来源、质量、特征)
3. **质量控制报告** (验证和统计)
4. **数据收集方法论** (可重复的流程)
5. **文献数据库** (完整的引用信息)

### 验收标准

- [ ] 序列数量 ≥ 1,000个
- [ ] 高活性序列比例 ≥ 70%
- [ ] 平均质量评分 ≥ 8.0
- [ ] 数据完整性 ≥ 95%
- [ ] 文献可追溯性 = 100%
- [ ] 适合机器学习训练

**项目成功！准备开始模型训练阶段。** 🎉
