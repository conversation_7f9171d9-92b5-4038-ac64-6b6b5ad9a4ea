{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ESM-2特征提取与数据集构建 (第3-4周)\n", "\n", "**作者**: ZK  \n", "**邮箱**: <EMAIL>  \n", "**日期**: 2025-07-20  \n", "**描述**: 使用ESM-2模型提取蛋白质序列特征，构建训练数据集\n", "\n", "## 目标\n", "1. 安装和配置ESM-2模型\n", "2. 批量提取序列特征\n", "3. 构建训练/验证/测试数据集\n", "4. 基于同源聚类的数据划分"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境检查与ESM-2安装"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查GPU环境\n", "import torch\n", "import sys\n", "import os\n", "\n", "print(f\"Python版本: {sys.version}\")\n", "print(f\"PyTorch版本: {torch.__version__}\")\n", "print(f\"CUDA可用: {torch.cuda.is_available()}\")\n", "\n", "if torch.cuda.is_available():\n", "    print(f\"CUDA版本: {torch.version.cuda}\")\n", "    print(f\"GPU数量: {torch.cuda.device_count()}\")\n", "    for i in range(torch.cuda.device_count()):\n", "        print(f\"  GPU {i}: {torch.cuda.get_device_name(i)}\")\n", "        print(f\"    显存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB\")\n", "else:\n", "    print(\"⚠️ 未检测到CUDA，将使用CPU（速度较慢）\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装ESM-2相关依赖\n", "import subprocess\n", "\n", "def install_package(package):\n", "    \"\"\"安装Python包\"\"\"\n", "    try:\n", "        subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", package])\n", "        print(f\"✅ 成功安装 {package}\")\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"❌ 安装 {package} 失败: {e}\")\n", "\n", "# ESM-2相关包\n", "esm_packages = [\n", "    \"fair-esm\",\n", "    \"transformers>=4.21.0\",\n", "    \"datasets>=2.13.0\",\n", "    \"scikit-learn>=1.3.0\",\n", "    \"umap-learn\",\n", "    \"plotly\"\n", "]\n", "\n", "print(\"开始安装ESM-2相关依赖...\")\n", "for package in esm_packages:\n", "    install_package(package)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import esm\n", "import torch\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import pandas as pd\n", "from Bio import SeqIO\n", "from sklearn.model_selection import StratifiedGroupKFold\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.cluster import KMeans\n", "from sklearn.decomposition import PCA\n", "from sklearn.manifold import TSNE\n", "import umap\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from tqdm import tqdm\n", "import pickle\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"✅ 库导入完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 路径配置和数据加载"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 路径配置\n", "PROJECT_ROOT = os.path.abspath(os.path.join(os.getcwd(), '..', '..'))\n", "DATA_PROCESSED_DIR = os.path.join(PROJECT_ROOT, 'data', 'processed')\n", "DATA_ANALYSIS_READY_DIR = os.path.join(PROJECT_ROOT, 'data', 'analysis_ready')\n", "\n", "# 创建输出目录\n", "os.makedirs(DATA_ANALYSIS_READY_DIR, exist_ok=True)\n", "\n", "print(f\"处理数据目录: {DATA_PROCESSED_DIR}\")\n", "print(f\"分析就绪目录: {DATA_ANALYSIS_READY_DIR}\")\n", "\n", "# 设备配置\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载处理后的数据\n", "def load_sequences_from_directory(data_dir):\n", "    \"\"\"从目录加载所有FASTA文件\"\"\"\n", "    sequences = []\n", "    labels = []\n", "    sources = []\n", "    \n", "    fasta_files = [f for f in os.listdir(data_dir) if f.endswith('.fasta')]\n", "    print(f\"发现 {len(fasta_files)} 个FASTA文件\")\n", "    \n", "    for filename in fasta_files:\n", "        filepath = os.path.join(data_dir, filename)\n", "        print(f\"加载: {filename}\")\n", "        \n", "        # 根据文件名判断标签\n", "        if 'negative' in filename.lower() or 'synthetic' in filename.lower() or 'uniprot' in filename.lower():\n", "            label = 0  # 负样本\n", "            source_type = 'negative'\n", "        else:\n", "            label = 1  # 正样本\n", "            source_type = 'positive'\n", "        \n", "        count = 0\n", "        for record in SeqIO.parse(filepath, \"fasta\"):\n", "            seq_str = str(record.seq)\n", "            if 10 <= len(seq_str) <= 200:  # 长度过滤\n", "                sequences.append(seq_str)\n", "                labels.append(label)\n", "                sources.append(f\"{source_type}_{filename}\")\n", "                count += 1\n", "        \n", "        print(f\"  加载了 {count} 个序列 (标签: {label})\")\n", "    \n", "    return sequences, labels, sources\n", "\n", "# 加载数据\n", "sequences, labels, sources = load_sequences_from_directory(DATA_PROCESSED_DIR)\n", "\n", "print(f\"\\n总序列数: {len(sequences)}\")\n", "print(f\"正样本: {sum(labels)} ({sum(labels)/len(labels)*100:.1f}%)\")\n", "print(f\"负样本: {len(labels)-sum(labels)} ({(len(labels)-sum(labels))/len(labels)*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. ESM-2特征提取器"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ESM2FeatureExtractor:\n", "    \"\"\"ESM-2特征提取器\"\"\"\n", "    \n", "    def __init__(self, model_name='esm2_t33_650M_UR50D', device='cuda'):\n", "        \"\"\"\n", "        初始化ESM-2模型\n", "        \n", "        可选模型:\n", "        - esm2_t6_8M_UR50D (最小，快速测试)\n", "        - esm2_t12_35M_UR50D\n", "        - esm2_t30_150M_UR50D\n", "        - esm2_t33_650M_UR50D (推荐，平衡性能)\n", "        - esm2_t36_3B_UR50D (最强，需要大显存)\n", "        \"\"\"\n", "        self.device = device\n", "        self.model_name = model_name\n", "        \n", "        print(f\"正在加载ESM-2模型: {model_name}\")\n", "        try:\n", "            self.model, self.alphabet = esm.pretrained.load_model_and_alphabet(model_name)\n", "            self.model = self.model.to(device)\n", "            self.model.eval()\n", "            self.batch_converter = self.alphabet.get_batch_converter()\n", "            \n", "            # 获取模型维度\n", "            self.embed_dim = self.model.embed_dim\n", "            print(f\"✅ 模型加载成功，嵌入维度: {self.embed_dim}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ 模型加载失败: {e}\")\n", "            raise\n", "    \n", "    def extract_features(self, sequences, batch_size=8, repr_layer=-1):\n", "        \"\"\"批量提取序列特征\"\"\"\n", "        print(f\"开始提取 {len(sequences)} 个序列的特征...\")\n", "        \n", "        all_features = []\n", "        \n", "        # 确定表示层\n", "        if repr_layer == -1:\n", "            repr_layer = self.model.num_layers\n", "        \n", "        with torch.no_grad():\n", "            for i in tqdm(range(0, len(sequences), batch_size), desc=\"提取特征\"):\n", "                batch_sequences = sequences[i:i+batch_size]\n", "                \n", "                # 准备批次数据\n", "                batch_data = [(f\"seq_{j}\", seq) for j, seq in enumerate(batch_sequences)]\n", "                batch_labels, batch_strs, batch_tokens = self.batch_converter(batch_data)\n", "                batch_tokens = batch_tokens.to(self.device)\n", "                \n", "                try:\n", "                    # 前向传播\n", "                    results = self.model(batch_tokens, repr_layers=[repr_layer])\n", "                    \n", "                    # 提取[CLS] token的表示 (位置0)\n", "                    representations = results[\"representations\"][repr_layer]\n", "                    cls_representations = representations[:, 0, :]\n", "                    \n", "                    all_features.append(cls_representations.cpu())\n", "                    \n", "                except RuntimeError as e:\n", "                    if \"out of memory\" in str(e):\n", "                        print(f\"⚠️ GPU内存不足，跳过批次 {i//batch_size + 1}\")\n", "                        torch.cuda.empty_cache()\n", "                        continue\n", "                    else:\n", "                        raise e\n", "        \n", "        if all_features:\n", "            features = torch.cat(all_features, dim=0)\n", "            print(f\"✅ 特征提取完成，形状: {features.shape}\")\n", "            return features.numpy()\n", "        else:\n", "            print(\"❌ 没有成功提取任何特征\")\n", "            return None\n", "    \n", "    def get_model_info(self):\n", "        \"\"\"获取模型信息\"\"\"\n", "        return {\n", "            'model_name': self.model_name,\n", "            'embed_dim': self.embed_dim,\n", "            'num_layers': self.model.num_layers,\n", "            'device': str(self.device)\n", "        }\n", "\n", "print(\"✅ ESM2FeatureExtractor类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 初始化模型并提取特征"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 根据可用显存选择模型\n", "if torch.cuda.is_available():\n", "    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3\n", "    print(f\"GPU显存: {gpu_memory:.1f} GB\")\n", "    \n", "    if gpu_memory >= 40:\n", "        model_name = 'esm2_t33_650M_UR50D'  # 推荐模型\n", "        batch_size = 16\n", "    elif gpu_memory >= 16:\n", "        model_name = 'esm2_t30_150M_UR50D'  # 中等模型\n", "        batch_size = 8\n", "    else:\n", "        model_name = 'esm2_t12_35M_UR50D'   # 小模型\n", "        batch_size = 4\n", "else:\n", "    model_name = 'esm2_t6_8M_UR50D'        # CPU使用最小模型\n", "    batch_size = 2\n", "\n", "print(f\"选择模型: {model_name}\")\n", "print(f\"批次大小: {batch_size}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化特征提取器\n", "extractor = ESM2FeatureExtractor(model_name=model_name, device=device)\n", "\n", "# 提取特征\n", "print(\"\\n开始特征提取...\")\n", "features = extractor.extract_features(sequences, batch_size=batch_size)\n", "\n", "if features is not None:\n", "    print(f\"特征矩阵形状: {features.shape}\")\n", "    print(f\"特征维度: {features.shape[1]}\")\n", "else:\n", "    print(\"❌ 特征提取失败\")\n", "    raise RuntimeError(\"特征提取失败，请检查模型和数据\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}