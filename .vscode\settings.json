{"python.defaultInterpreterPath": "~/miniconda3/envs/amp_esm2/bin/python", "jupyter.jupyterServerType": "remote", "jupyter.allowUnauthorizedRemoteConnection": true, "jupyter.askForKernelRestart": false, "jupyter.interactiveWindow.textEditor.executeSelection": true, "jupyter.sendSelectionToInteractiveWindow": true, "jupyter.notebookFileRoot": "${workspaceFolder}", "jupyter.defaultKernel": "amp_esm2", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "terminal.integrated.env.linux": {"CONDA_DEFAULT_ENV": "amp_esm2", "JUPYTER_CONFIG_DIR": "${workspaceFolder}/configs"}, "files.associations": {"*.ipynb": "jupyter-notebook"}, "jupyter.kernels.filter": [{"path": "~/miniconda3/envs/amp_esm2/bin/python", "type": "pythonEnvironment"}], "remote.SSH.remotePlatform": {"**************": "linux"}, "remote.SSH.configFile": "~/.ssh/config", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "jupyter.experimental.debugging": true, "jupyter.logging.level": "info", "workbench.colorCustomizations": {"statusBar.background": "#1e7e34", "statusBar.foreground": "#ffffff", "statusBarItem.hoverBackground": "#28a745"}, "window.title": "ESM-2 抗菌肽项目 - HPC环境"}