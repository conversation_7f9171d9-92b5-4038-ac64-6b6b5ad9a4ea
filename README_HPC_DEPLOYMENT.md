# 🧬 ESM-2抗菌肽项目 - 高算平台部署指南

## 📋 项目概述
本项目基于ESM-2预训练模型构建高精度的抗菌肽(AMP)分类器，专门优化用于在高算平台上以Jupyter Notebook形式运行。

## 🚀 快速部署（一键式）

### 方法1：自动部署脚本
```bash
# 1. 上传项目到高算平台
scp -r /path/to/local/project/* username@hpc-cluster:~/amp_project/

# 2. 连接到高算平台
ssh username@hpc-cluster
cd ~/amp_project

# 3. 运行一键部署脚本
chmod +x deploy_to_hpc.sh
./deploy_to_hpc.sh
```

### 方法2：手动部署步骤

#### 第一步：环境配置
```bash
# 给脚本添加执行权限
chmod +x scripts/setup_hpc_environment.sh
chmod +x scripts/run_jupyter_hpc.sh

# 运行环境配置
./scripts/setup_hpc_environment.sh

# 验证环境
source activate_env.sh
python test_environment.py
```

#### 第二步：启动Jupyter Lab
```bash
# 提交Jupyter作业
sbatch scripts/run_jupyter_hpc.sh

# 查看作业状态
squeue -u $USER

# 查看作业输出（获取访问信息）
tail -f logs/jupyter_<job_id>.out
```

#### 第三步：建立SSH隧道
在本地终端执行：
```bash
# 从日志中获取节点IP和端口，然后建立隧道
ssh -L 8888:节点IP:8888 username@hpc-cluster

# 在浏览器中访问
http://localhost:8888
```

## 📊 Jupyter Notebook运行指南

### 1. 环境配置单元
在运行主要的`ESM2_AMP_Training_Complete.ipynb`之前，请先运行`hpc_notebook_setup.ipynb`中的配置单元，或者在主notebook开头添加以下代码：

```python
# 高算平台环境配置
exec(open('scripts/notebook_hpc_setup.py').read())
```

### 2. 关键配置参数
配置单元会自动设置以下参数：
- **批处理大小**: 根据可用内存自动调整（2-32）
- **ESM模型选择**: 根据内存自动选择合适的模型大小
- **CPU线程数**: 使用SLURM分配的CPU核心数
- **内存监控**: 实时监控和清理内存

### 3. 训练过程优化
```python
# 在训练循环中添加内存监控
for epoch in range(num_epochs):
    # 训练代码...
    
    # 每5个epoch监控内存
    if epoch % 5 == 0:
        monitor_memory()
        clear_memory()
    
    # 保存检查点
    if epoch % 10 == 0:
        checkpoint_manager.save_checkpoint(
            model, optimizer, epoch, loss
        )
```

## 🔧 资源配置建议

### CPU节点配置
```bash
#SBATCH --partition=cpu
#SBATCH --cpus-per-task=16      # 16核CPU
#SBATCH --mem=64G               # 64GB内存
#SBATCH --time=12:00:00         # 12小时
```

### GPU节点配置（可选）
```bash
#SBATCH --partition=gpu
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=08:00:00
```

## 📁 项目文件结构
```
amp_project/
├── ESM2_AMP_Training_Complete.ipynb  # 主要训练notebook
├── hpc_notebook_setup.ipynb          # 高算平台配置单元
├── requirements.txt                   # Python依赖
├── deploy_to_hpc.sh                  # 一键部署脚本
├── scripts/
│   ├── setup_hpc_environment.sh     # 环境配置脚本
│   ├── run_jupyter_hpc.sh           # Jupyter启动脚本
│   └── notebook_hpc_setup.py        # Notebook配置脚本
├── configs/
│   └── jupyter_config.py            # Jupyter配置文件
├── docs/
│   └── jupyter_hpc_guide.md         # 详细使用指南
└── data/, models/, results/, logs/   # 数据和结果目录
```

## 🛠️ 常见问题解决

### 1. 内存不足
```python
# 解决方案：减小批处理大小
BATCH_SIZE = 4  # 从默认的16或32减小到4

# 或使用更小的ESM模型
model, alphabet = esm.pretrained.esm2_t6_8M_UR50D()  # 8M参数
```

### 2. 训练中断恢复
```python
# 检查是否有检查点
latest_checkpoint = checkpoint_manager.get_latest_checkpoint()
if latest_checkpoint:
    start_epoch, last_loss, metrics = checkpoint_manager.load_checkpoint(
        model, optimizer, latest_checkpoint.name
    )
    print(f"从第{start_epoch}轮继续训练")
```

### 3. Jupyter连接问题
```bash
# 检查作业状态
squeue -u $USER

# 查看详细日志
cat logs/jupyter_<job_id>.out

# 重新建立SSH隧道
ssh -L 8888:节点IP:8888 username@hpc-cluster
```

## 📈 性能监控

### 实时监控命令
```bash
# 查看作业状态
squeue -u $USER

# 监控资源使用
scontrol show job <job_id>

# 查看实时日志
tail -f logs/jupyter_<job_id>.out
```

### Notebook内监控
```python
# 内存使用监控
monitor_memory()

# 训练进度可视化
plot_realtime_loss(training_losses)

# 检查点列表
checkpoints = checkpoint_manager.list_checkpoints()
print(f"已保存{len(checkpoints)}个检查点")
```

## 🎯 最佳实践

1. **分阶段运行**: 先运行小批量测试，确认无误后再进行完整训练
2. **定期保存**: 每5-10个epoch保存检查点，防止意外中断
3. **内存管理**: 定期调用`clear_memory()`清理内存
4. **资源监控**: 使用`monitor_memory()`监控资源使用
5. **错误处理**: 在关键代码块添加try-catch处理

## 📞 技术支持

- **邮箱**: <EMAIL>
- **文档**: `docs/jupyter_hpc_guide.md`
- **日志位置**: `logs/`目录
- **检查点**: `results/checkpoints/`目录

## 🎉 开始使用

1. 完成环境部署后，在浏览器中访问Jupyter Lab
2. 首先运行`hpc_notebook_setup.ipynb`进行环境配置
3. 然后运行`ESM2_AMP_Training_Complete.ipynb`开始训练
4. 监控训练过程，定期保存检查点
5. 训练完成后下载结果文件

---

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-01-27  
**版本**: 1.0

祝您实验顺利！🧬✨
