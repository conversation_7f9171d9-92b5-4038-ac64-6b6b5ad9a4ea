{"cells": [{"cell_type": "code", "execution_count": 1, "id": "09300552-3898-40da-9301-3e24a8364e58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python版本: 3.9.23 (main, Jun  5 2025, 13:40:20) \n", "[GCC 11.2.0]\n", "操作系统: Linux 6.6.87.2-microsoft-standard-WSL2\n", "架构: x86_64\n"]}], "source": ["# 检查Python版本和基础环境\n", "import sys\n", "import platform\n", "print(f\"Python版本: {sys.version}\")\n", "print(f\"操作系统: {platform.system()} {platform.release()}\")\n", "print(f\"架构: {platform.machine()}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "226788c8-ce13-4793-9d6c-fe7fb28de436", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始安装依赖包...\n", "Collecting biopython>=1.81\n", "  Downloading biopython-1.85-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (13 kB)\n", "Requirement already satisfied: numpy in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from biopython>=1.81) (2.0.1)\n", "Downloading biopython-1.85-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.2/3.2 MB\u001b[0m \u001b[31m11.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: biopython\n", "Successfully installed biopython-1.85\n", "✅ 成功安装 biopython>=1.81\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pandas>=1.5.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (2.3.1)\n", "Requirement already satisfied: numpy>=1.22.4 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from pandas>=1.5.0) (2.0.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from pandas>=1.5.0) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from pandas>=1.5.0) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from pandas>=1.5.0) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from python-dateutil>=2.8.2->pandas>=1.5.0) (1.17.0)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ 成功安装 pandas>=1.5.0\n", "Requirement already satisfied: numpy>=1.24.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (2.0.1)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ 成功安装 numpy>=1.24.0\n", "Requirement already satisfied: requests>=2.28.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (2.32.4)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from requests>=2.28.0) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from requests>=2.28.0) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from requests>=2.28.0) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from requests>=2.28.0) (2025.7.14)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ 成功安装 requests>=2.28.0\n", "Requirement already satisfied: tqdm>=4.64.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (4.67.1)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ 成功安装 tqdm>=4.64.0\n", "Requirement already satisfied: matplotlib>=3.6.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (3.9.4)\n", "Requirement already satisfied: contourpy>=1.0.1 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib>=3.6.0) (1.3.0)\n", "Requirement already satisfied: cycler>=0.10 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib>=3.6.0) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib>=3.6.0) (4.59.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib>=3.6.0) (1.4.7)\n", "Requirement already satisfied: numpy>=1.23 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib>=3.6.0) (2.0.1)\n", "Requirement already satisfied: packaging>=20.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib>=3.6.0) (25.0)\n", "Requirement already satisfied: pillow>=8 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib>=3.6.0) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib>=3.6.0) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib>=3.6.0) (2.9.0.post0)\n", "Requirement already satisfied: importlib-resources>=3.2.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib>=3.6.0) (6.5.2)\n", "Requirement already satisfied: zipp>=3.1.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from importlib-resources>=3.2.0->matplotlib>=3.6.0) (3.23.0)\n", "Requirement already satisfied: six>=1.5 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from python-dateutil>=2.7->matplotlib>=3.6.0) (1.17.0)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ 成功安装 matplotlib>=3.6.0\n", "Requirement already satisfied: seaborn>=0.12.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (0.13.2)\n", "Requirement already satisfied: numpy!=1.24.0,>=1.20 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from seaborn>=0.12.0) (2.0.1)\n", "Requirement already satisfied: pandas>=1.2 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from seaborn>=0.12.0) (2.3.1)\n", "Requirement already satisfied: matplotlib!=3.6.1,>=3.4 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from seaborn>=0.12.0) (3.9.4)\n", "Requirement already satisfied: contourpy>=1.0.1 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (1.3.0)\n", "Requirement already satisfied: cycler>=0.10 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (4.59.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (1.4.7)\n", "Requirement already satisfied: packaging>=20.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (25.0)\n", "Requirement already satisfied: pillow>=8 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (2.9.0.post0)\n", "Requirement already satisfied: importlib-resources>=3.2.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (6.5.2)\n", "Requirement already satisfied: zipp>=3.1.0 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from importlib-resources>=3.2.0->matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (3.23.0)\n", "Requirement already satisfied: pytz>=2020.1 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from pandas>=1.2->seaborn>=0.12.0) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from pandas>=1.2->seaborn>=0.12.0) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /root/miniconda3/envs/amp_esm2/lib/python3.9/site-packages (from python-dateutil>=2.7->matplotlib!=3.6.1,>=3.4->seaborn>=0.12.0) (1.17.0)\n", "✅ 成功安装 seaborn>=0.12.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["# 安装必要的依赖包\n", "import subprocess\n", "import sys\n", "\n", "def install_package(package):\n", "    \"\"\"安装Python包的辅助函数\"\"\"\n", "    try:\n", "        subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", package])\n", "        print(f\"✅ 成功安装 {package}\")\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"❌ 安装 {package} 失败: {e}\")\n", "\n", "# 核心依赖包列表\n", "required_packages = [\n", "    \"biopython>=1.81\",\n", "    \"pandas>=1.5.0\",\n", "    \"numpy>=1.24.0\",\n", "    \"requests>=2.28.0\",\n", "    \"tqdm>=4.64.0\",\n", "    \"matplotlib>=3.6.0\",\n", "    \"seaborn>=0.12.0\"\n", "]\n", "\n", "print(\"开始安装依赖包...\")\n", "for package in required_packages:\n", "    install_package(package)"]}], "metadata": {"kernelspec": {"display_name": "AMP ESM-2", "language": "python", "name": "amp_esm2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}