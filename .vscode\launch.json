{"version": "0.2.0", "configurations": [{"name": "🧬 调试ESM-2训练", "type": "python", "request": "launch", "program": "${workspaceFolder}/scripts/notebook_hpc_setup.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "amp_esm2"}, "args": [], "justMyCode": false}, {"name": "🔍 调试数据处理", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "amp_esm2"}, "args": [], "justMyCode": true}, {"name": "🧪 运行兼容性测试", "type": "python", "request": "launch", "program": "${workspaceFolder}/scripts/verify_esm_compatibility.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "amp_esm2"}, "args": [], "justMyCode": false}]}