#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 快速启动ESM-2调试会话

echo "🚀 启动ESM-2调试会话"
echo "平台: s20223050931@**************"
echo "=========================================="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }

# 检查必要文件
if [ ! -f "scripts/debug_on_cpu_node.sh" ]; then
    echo "❌ 缺少调试脚本，请确保项目文件完整"
    exit 1
fi

# 给脚本添加执行权限
chmod +x scripts/debug_on_cpu_node.sh
chmod +x scripts/debug_helper.py

print_info "准备启动调试会话..."
echo ""
echo "📋 调试会话选项:"
echo "1. 🖥️  申请CPU节点进行交互式调试（推荐）"
echo "2. 🧪 在当前环境运行快速检查"
echo "3. 📊 生成环境诊断报告"
echo "4. 🚀 启动Jupyter Lab调试环境"
echo ""

read -p "请选择调试方式 (1-4): " choice

case $choice in
    1)
        print_info "启动CPU节点交互式调试..."
        print_warning "这将申请一个CPU节点，请确保您有足够的计算资源配额"
        echo ""
        echo "📋 将申请的资源:"
        echo "  - CPU: 16核"
        echo "  - 内存: 64GB"
        echo "  - 时间: 4小时"
        echo "  - 分区: cpu"
        echo ""
        read -p "确认申请CPU节点？(y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            ./scripts/debug_on_cpu_node.sh
        else
            echo "取消申请"
        fi
        ;;
    2)
        print_info "运行快速环境检查..."
        python scripts/debug_helper.py
        ;;
    3)
        print_info "生成环境诊断报告..."
        python scripts/debug_helper.py > debug_report_$(date +%Y%m%d_%H%M%S).log 2>&1
        print_success "诊断报告已生成"
        ;;
    4)
        print_info "启动Jupyter Lab调试环境..."
        
        # 检查是否在SLURM环境中
        if [[ -n "$SLURM_JOB_ID" ]]; then
            print_success "检测到SLURM环境，直接启动Jupyter"
            
            # 激活环境
            source ~/miniconda3/etc/profile.d/conda.sh || source ~/anaconda3/etc/profile.d/conda.sh
            conda activate amp_esm2
            
            # 获取节点信息
            NODE_IP=$(hostname -i)
            PORT=8888
            
            echo "🌐 Jupyter访问信息:"
            echo "  节点IP: $NODE_IP"
            echo "  端口: $PORT"
            echo "  访问地址: http://$NODE_IP:$PORT"
            echo ""
            echo "🔗 SSH隧道命令 (在本地运行):"
            echo "  ssh -L $PORT:$NODE_IP:$PORT s20223050931@**************"
            echo ""
            
            # 启动Jupyter
            jupyter lab \
                --ip=0.0.0.0 \
                --port=$PORT \
                --no-browser \
                --allow-root \
                --notebook-dir=$(pwd) \
                --ServerApp.token='' \
                --ServerApp.password='' \
                --ServerApp.allow_origin='*' \
                --ServerApp.disable_check_xsrf=True \
                --ServerApp.allow_remote_access=True
        else
            print_warning "未检测到SLURM环境，建议先申请CPU节点"
            echo "请选择选项1申请CPU节点后再启动Jupyter"
        fi
        ;;
    *)
        echo "无效选择，退出"
        exit 1
        ;;
esac

print_success "调试会话结束"
