#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 一键部署ESM-2抗菌肽项目到高算平台

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🚀 ESM-2抗菌肽项目 - 高算平台一键部署"
echo "=========================================="

# 检查必要文件
print_info "检查项目文件..."
required_files=(
    "ESM2_AMP_Training_Complete.ipynb"
    "requirements.txt"
    "scripts/setup_hpc_environment.sh"
    "scripts/run_jupyter_hpc.sh"
    "configs/jupyter_config.py"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "找到文件: $file"
    else
        print_error "缺少文件: $file"
        exit 1
    fi
done

# 给脚本添加执行权限
print_info "设置脚本权限..."
chmod +x scripts/setup_hpc_environment.sh
chmod +x scripts/run_jupyter_hpc.sh
chmod +x deploy_to_hpc.sh
print_success "权限设置完成"

# 运行环境配置
print_info "开始环境配置..."
if ./scripts/setup_hpc_environment.sh; then
    print_success "环境配置完成"
else
    print_error "环境配置失败"
    exit 1
fi

# 验证环境
print_info "验证环境..."
source activate_env.sh
if python test_environment.py; then
    print_success "环境验证通过"
else
    print_error "环境验证失败"
    exit 1
fi

# 提交Jupyter作业
print_info "提交Jupyter Lab作业..."
JOB_ID=$(sbatch --parsable scripts/run_jupyter_hpc.sh)

if [ $? -eq 0 ]; then
    print_success "作业提交成功，作业ID: $JOB_ID"
    
    echo ""
    echo "📋 下一步操作:"
    echo "1. 查看作业状态: squeue -u \$USER"
    echo "2. 查看作业输出: tail -f logs/jupyter_${JOB_ID}.out"
    echo "3. 等待Jupyter启动后，从日志中获取访问信息"
    echo "4. 建立SSH隧道: ssh -L 8888:节点IP:8888 用户名@集群地址"
    echo "5. 浏览器访问: http://localhost:8888"
    echo ""
    echo "📖 详细说明请参考: docs/jupyter_hpc_guide.md"
    echo ""
    print_success "部署完成！祝您实验顺利！🧬"
else
    print_error "作业提交失败"
    exit 1
fi
