# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-21
# 描述: 抗革兰氏阴性菌肽数据收集质量控制工具

import pandas as pd
import numpy as np
import re
from datetime import datetime
from Bio.Seq import Seq
from Bio import pairwise2
import matplotlib.pyplot as plt
import seaborn as sns

class AMPDataQualityController:
    """抗菌肽数据质量控制器"""
    
    def __init__(self):
        self.gram_negative_organisms = [
            'Escherichia coli', 'E. coli', 'E.coli',
            'Pseudomonas aeruginosa', 'P. aeruginosa', 'P.aeruginosa',
            'Acinetobacter baumannii', '<PERSON>. baumannii', '<PERSON><PERSON>b<PERSON>mannii',
            'Klebsiella pneumoniae', 'K. pneumoniae', 'K.pneumoniae',
            'Salmonella enterica', 'Salmonella', 'S. enterica',
            'Enterobacter', 'Proteus', 'Serratia', 'Citrobacter'
        ]
        
        self.standard_amino_acids = set('ACDEFGHIKLMNPQRSTVWY')
        
    def validate_amino_acid_sequence(self, sequence):
        """验证氨基酸序列"""
        if not sequence or not isinstance(sequence, str):
            return False, "序列为空或格式错误"
        
        sequence = sequence.upper().strip()
        
        # 检查长度
        if len(sequence) < 5:
            return False, "序列过短(<5 AA)"
        if len(sequence) > 100:
            return False, "序列过长(>100 AA)"
        
        # 检查字符
        invalid_chars = set(sequence) - self.standard_amino_acids
        if invalid_chars:
            invalid_ratio = len([c for c in sequence if c in invalid_chars]) / len(sequence)
            if invalid_ratio > 0.1:
                return False, f"包含过多非标准氨基酸: {invalid_chars}"
            else:
                return True, f"包含少量非标准氨基酸: {invalid_chars} (可接受)"
        
        return True, "序列有效"
    
    def validate_mic_data(self, mic_value, mic_unit):
        """验证MIC数据"""
        try:
            mic_float = float(mic_value)
        except (ValueError, TypeError):
            return False, "MIC值不是有效数字"
        
        if mic_float <= 0:
            return False, "MIC值必须为正数"
        
        if mic_float > 1000:
            return False, "MIC值异常高(>1000)"
        
        valid_units = ['μg/mL', 'μM', 'mg/L', 'μg/ml', 'ug/mL', 'ug/ml']
        if mic_unit not in valid_units:
            return False, f"MIC单位无效: {mic_unit}"
        
        return True, "MIC数据有效"
    
    def validate_target_organism(self, organism):
        """验证目标菌株"""
        if not organism or not isinstance(organism, str):
            return False, "菌株信息为空"
        
        organism_lower = organism.lower()
        
        # 检查是否为革兰氏阴性菌
        for gn_org in self.gram_negative_organisms:
            if gn_org.lower() in organism_lower:
                return True, f"确认为革兰氏阴性菌: {gn_org}"
        
        # 检查是否明确标注为革兰氏阴性
        if 'gram-negative' in organism_lower or 'gram negative' in organism_lower:
            return True, "标注为革兰氏阴性菌"
        
        return False, "无法确认为革兰氏阴性菌"
    
    def calculate_quality_score(self, record):
        """计算质量评分"""
        score = 0.0
        
        # MIC值评分 (40%)
        try:
            mic_value = float(record.get('MIC_Value', 999))
            if mic_value <= 4:
                score += 4.0
            elif mic_value <= 8:
                score += 3.5
            elif mic_value <= 16:
                score += 3.0
            elif mic_value <= 32:
                score += 2.0
            else:
                score += 1.0
        except:
            score += 0.5
        
        # 菌株特异性评分 (20%)
        organism = record.get('Target_Organism', '')
        is_valid, _ = self.validate_target_organism(organism)
        if is_valid:
            if any(org in organism.lower() for org in ['e. coli', 'e.coli']):
                score += 2.0
            elif any(org in organism.lower() for org in ['p. aeruginosa', 'p.aeruginosa']):
                score += 2.0
            elif any(org in organism.lower() for org in ['a. baumannii', 'a.baumannii']):
                score += 2.0
            else:
                score += 1.5
        else:
            score += 0.5
        
        # 序列长度评分 (15%)
        try:
            length = int(record.get('Length', 0))
            if 15 <= length <= 35:
                score += 1.5
            elif 10 <= length <= 50:
                score += 1.2
            else:
                score += 0.8
        except:
            score += 0.5
        
        # 文献质量评分 (15%)
        pmid = record.get('Reference_PMID', '')
        doi = record.get('Reference_DOI', '')
        if pmid and doi:
            score += 1.5
        elif pmid or doi:
            score += 1.0
        else:
            score += 0.5
        
        # 数据完整性评分 (10%)
        required_fields = ['Sequence', 'Target_Organism', 'MIC_Value', 'MIC_Unit']
        completeness = sum([bool(record.get(field)) for field in required_fields]) / len(required_fields)
        score += completeness * 1.0
        
        return round(score, 1)
    
    def check_sequence_similarity(self, seq1, seq2):
        """计算序列相似度"""
        try:
            alignments = pairwise2.align.globalxx(seq1.upper(), seq2.upper())
            if alignments:
                alignment = alignments[0]
                matches = sum(1 for a, b in zip(alignment[0], alignment[1]) if a == b and a != '-')
                total_length = max(len(seq1), len(seq2))
                similarity = matches / total_length
                return similarity
            else:
                return 0.0
        except:
            return 0.0
    
    def find_duplicates(self, data, similarity_threshold=0.95):
        """查找重复序列"""
        duplicates = []
        
        for i in range(len(data)):
            for j in range(i+1, len(data)):
                seq1 = data.iloc[i]['Sequence']
                seq2 = data.iloc[j]['Sequence']
                
                similarity = self.check_sequence_similarity(seq1, seq2)
                
                if similarity >= similarity_threshold:
                    duplicates.append({
                        'index1': i,
                        'index2': j,
                        'peptide1': data.iloc[i]['Peptide_ID'],
                        'peptide2': data.iloc[j]['Peptide_ID'],
                        'similarity': similarity,
                        'sequence1': seq1,
                        'sequence2': seq2
                    })
        
        return duplicates
    
    def generate_quality_report(self, data):
        """生成质量报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_records': len(data),
            'quality_stats': {},
            'validation_results': {},
            'recommendations': []
        }
        
        # 计算质量评分
        quality_scores = []
        validation_results = {
            'valid_sequences': 0,
            'valid_mic_data': 0,
            'valid_organisms': 0,
            'complete_records': 0
        }
        
        for idx, record in data.iterrows():
            # 序列验证
            seq_valid, seq_msg = self.validate_amino_acid_sequence(record.get('Sequence', ''))
            if seq_valid:
                validation_results['valid_sequences'] += 1
            
            # MIC验证
            mic_valid, mic_msg = self.validate_mic_data(
                record.get('MIC_Value'), 
                record.get('MIC_Unit')
            )
            if mic_valid:
                validation_results['valid_mic_data'] += 1
            
            # 菌株验证
            org_valid, org_msg = self.validate_target_organism(record.get('Target_Organism'))
            if org_valid:
                validation_results['valid_organisms'] += 1
            
            # 完整性检查
            required_fields = ['Peptide_ID', 'Sequence', 'Target_Organism', 'MIC_Value']
            if all(record.get(field) for field in required_fields):
                validation_results['complete_records'] += 1
            
            # 质量评分
            quality_score = self.calculate_quality_score(record)
            quality_scores.append(quality_score)
        
        # 统计信息
        report['quality_stats'] = {
            'mean_quality_score': np.mean(quality_scores),
            'median_quality_score': np.median(quality_scores),
            'min_quality_score': np.min(quality_scores),
            'max_quality_score': np.max(quality_scores),
            'high_quality_count': sum(1 for score in quality_scores if score >= 8.0),
            'medium_quality_count': sum(1 for score in quality_scores if 6.0 <= score < 8.0),
            'low_quality_count': sum(1 for score in quality_scores if score < 6.0)
        }
        
        report['validation_results'] = validation_results
        
        # 生成建议
        if validation_results['valid_sequences'] / len(data) < 0.95:
            report['recommendations'].append("序列验证通过率较低，需要检查序列格式")
        
        if validation_results['valid_mic_data'] / len(data) < 0.90:
            report['recommendations'].append("MIC数据验证通过率较低，需要检查数据格式")
        
        if report['quality_stats']['mean_quality_score'] < 7.0:
            report['recommendations'].append("平均质量评分较低，建议提高数据收集标准")
        
        return report
    
    def visualize_quality_distribution(self, data, save_path=None):
        """可视化质量分布"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 质量评分分布
        quality_scores = [self.calculate_quality_score(record) for _, record in data.iterrows()]
        axes[0, 0].hist(quality_scores, bins=20, alpha=0.7, color='skyblue')
        axes[0, 0].set_title('质量评分分布')
        axes[0, 0].set_xlabel('质量评分')
        axes[0, 0].set_ylabel('频次')
        
        # MIC值分布
        mic_values = pd.to_numeric(data['MIC_Value'], errors='coerce').dropna()
        axes[0, 1].hist(np.log10(mic_values), bins=20, alpha=0.7, color='lightgreen')
        axes[0, 1].set_title('MIC值分布 (log10)')
        axes[0, 1].set_xlabel('log10(MIC)')
        axes[0, 1].set_ylabel('频次')
        
        # 序列长度分布
        lengths = pd.to_numeric(data['Length'], errors='coerce').dropna()
        axes[1, 0].hist(lengths, bins=20, alpha=0.7, color='salmon')
        axes[1, 0].set_title('序列长度分布')
        axes[1, 0].set_xlabel('序列长度 (AA)')
        axes[1, 0].set_ylabel('频次')
        
        # 菌株分布
        organism_counts = data['Target_Organism'].value_counts().head(10)
        axes[1, 1].bar(range(len(organism_counts)), organism_counts.values)
        axes[1, 1].set_title('目标菌株分布 (Top 10)')
        axes[1, 1].set_xlabel('菌株')
        axes[1, 1].set_ylabel('序列数量')
        axes[1, 1].set_xticks(range(len(organism_counts)))
        axes[1, 1].set_xticklabels(organism_counts.index, rotation=45, ha='right')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
        
        return fig

def main():
    """示例用法"""
    # 创建质量控制器
    qc = AMPDataQualityController()
    
    # 加载数据
    data = pd.read_csv('templates/data_collection_template.csv')
    
    # 生成质量报告
    report = qc.generate_quality_report(data)
    
    print("质量控制报告:")
    print(f"总记录数: {report['total_records']}")
    print(f"平均质量评分: {report['quality_stats']['mean_quality_score']:.2f}")
    print(f"高质量记录数: {report['quality_stats']['high_quality_count']}")
    
    # 查找重复序列
    duplicates = qc.find_duplicates(data)
    print(f"发现重复序列: {len(duplicates)}对")
    
    # 可视化
    qc.visualize_quality_distribution(data)

if __name__ == "__main__":
    main()
