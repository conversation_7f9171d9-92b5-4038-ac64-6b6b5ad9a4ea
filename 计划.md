好的，我为您制定一个完整的基于 ESM-2 的 AMP 分类项目计划。这个计划将确保数据质量、处理流程规范，并充分发挥 ESM-2 的优势。

# AMP 分类项目：ESM-2 方案详细计划

## 项目结构
```
amp_esm2_project/
├── README.md
├── requirements.txt
├── config.yaml              # 所有配置参数
├── data/
│   ├── raw/                 # 原始下载
│   ├── interim/             # 中间处理
│   ├── processed/           # 最终数据
│   └── external/            # 外部验证集
├── src/
│   ├── data/
│   │   ├── download.py      # 数据下载
│   │   ├── clean.py         # 数据清洗
│   │   ├── split.py         # 数据划分
│   │   └── features.py      # 特征提取
│   ├── models/
│   │   ├── esm2_amp.py      # 模型定义
│   │   └── train.py         # 训练脚本
│   └── evaluation/
│       └── evaluate.py      # 评估脚本
├── notebooks/
│   ├── 01_data_exploration.ipynb
│   └── 02_results_analysis.ipynb
└── logs/
```

## 阶段一：数据收集与初步处理（第1-2周）

### 1.1 正样本（AMP）数据下载

**下载脚本核心逻辑** (`src/data/download.py`)：

```python
import requests
import os
from Bio import SeqIO
import pandas as pd
from datetime import datetime

class AMPDownloader:
    def __init__(self, output_dir='data/raw'):
        self.output_dir = output_dir
        self.sources = {
            'dbAMP': {
                'url': 'http://csb.cse.yzu.edu.tw/dbAMP/download/dbAMP_pos.fasta',
                'backup': 'https://github.com/batriste/dbAMP/raw/main/data/dbAMP_pos.fasta'
            },
            'APD3': {
                'url': 'https://aps.unmc.edu/AP/database/mysql.php',
                'method': 'post',  # 需要特殊处理
                'params': {'database': 'APD', 'query_type': 'all'}
            },
            'CAMP': {
                'url': 'http://www.camp3.bicnirrh.res.in/campdownload.php',
                'file': 'CAMP_AMP_sequences.fasta'
            },
            'DRAMP': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php',
                'files': ['DRAMP_general_amps.fasta', 'DRAMP_patent_amps.fasta']
            },
            'DBAASP': {
                'url': 'https://dbaasp.org/download',
                'params': {'type': 'fasta', 'activity': 'antibacterial'}
            }
        }
        
    def download_all(self):
        """下载所有数据库"""
        metadata = []
        for db_name, config in self.sources.items():
            print(f"Downloading {db_name}...")
            # 实现具体下载逻辑
            # 记录元数据：来源、下载时间、序列数等
```

**数据质量检查点**：
- 检查文件完整性（MD5/SHA256）
- 统计每个库的序列数量
- 检查序列格式是否标准 FASTA
- 记录异常字符（如 B, Z, X）

### 1.2 负样本数据构建策略

**三重负样本策略**：

```python
class NegativeSampleBuilder:
    def __init__(self):
        self.sources = {
            'uniprot_reviewed': {
                'query': 'reviewed:true AND length:[10 TO 300] AND NOT annotation:(antimicrobial OR antibiotic OR antifungal OR antiviral OR defensin OR bacteriocin)',
                'filters': ['organism:"Homo sapiens"', 'organism:"Mus musculus"', 'organism:"Escherichia coli"']
            },
            'pdb_short_peptides': {
                'query': 'length:[10 TO 50] AND experimental_method:X-RAY',
                'exclude_keywords': ['antimicrobial', 'antibiotic', 'toxin']
            },
            'synthetic_negatives': {
                'method': 'shuffle_preserve_composition',
                'source': 'natural_proteins'
            }
        }
    
    def download_uniprot(self):
        """UniProt REST API 批量下载"""
        base_url = "https://rest.uniprot.org/uniprotkb/stream"
        # 分批下载，每批 500 条，避免超时
        
    def generate_synthetic(self, positive_amps):
        """生成合成负样本"""
        # 1. 打乱序列但保持氨基酸组成
        # 2. 反转序列
        # 3. 随机组合已知非AMP片段
```

## 阶段二：数据清洗与标准化（第2-3周）

### 2.1 序列清洗标准

```python
class SequenceCleaner:
    def __init__(self, config):
        self.min_length = config['min_length']  # 10
        self.max_length = config['max_length']  # 200 (ESM-2 推荐)
        self.valid_aa = set('ACDEFGHIKLMNPQRSTVWY')
        self.max_unknown_ratio = 0.1  # 最多 10% X
        
    def clean_sequence(self, seq):
        """清洗单条序列"""
        # 1. 转大写
        seq = seq.upper()
        
        # 2. 替换异常氨基酸
        replacements = {'B': 'N', 'Z': 'Q', 'U': 'C', 'O': 'K'}
        for old, new in replacements.items():
            seq = seq.replace(old, new)
        
        # 3. 检查 X 比例
        x_ratio = seq.count('X') / len(seq)
        if x_ratio > self.max_unknown_ratio:
            return None
            
        # 4. 长度过滤
        if not (self.min_length <= len(seq) <= self.max_length):
            return None
            
        return seq
```

### 2.2 去冗余策略（关键步骤）

```python
class RedundancyRemover:
    def __init__(self):
        self.identity_threshold = 0.4  # 40% 序列相似度
        self.coverage_threshold = 0.8  # 80% 覆盖度
        
    def mmseqs_clustering(self, fasta_file):
        """使用 MMseqs2 进行序列聚类"""
        cmd = f"""
        mmseqs easy-cluster {fasta_file} clustered tmp \
            --min-seq-id {self.identity_threshold} \
            -c {self.coverage_threshold} \
            --cov-mode 1 \
            --cluster-mode 1 \
            --threads 16
        """
        # 返回代表序列
        
    def cd_hit_backup(self, fasta_file):
        """备用方案：CD-HIT"""
        cmd = f"""
        cd-hit -i {fasta_file} -o nr_seqs.fasta \
            -c {self.identity_threshold} \
            -n 2 \
            -M 0 \
            -T 16
        """
```

## 阶段三：ESM-2 特征提取与数据集构建（第3-4周）

### 3.1 ESM-2 模型选择与配置

```python
class ESM2FeatureExtractor:
    def __init__(self, model_name='esm2_t33_650M_UR50D'):
        """
        可选模型：
        - esm2_t6_8M_UR50D (最小，快速测试)
        - esm2_t12_35M_UR50D
        - esm2_t30_150M_UR50D
        - esm2_t33_650M_UR50D (推荐，平衡性能)
        - esm2_t36_3B_UR50D (最强，需要 A100 40GB)
        """
        import esm
        self.model, self.alphabet = esm.pretrained.load_model_and_alphabet(model_name)
        self.model.eval()
        self.batch_converter = self.alphabet.get_batch_converter()
        
    def extract_features(self, sequences, batch_size=8):
        """批量提取特征"""
        features = []
        for i in range(0, len(sequences), batch_size):
            batch = sequences[i:i+batch_size]
            # 准备输入
            batch_labels, batch_strs, batch_tokens = self.batch_converter(
                [(f"seq_{j}", seq) for j, seq in enumerate(batch)]
            )
            
            with torch.no_grad():
                results = self.model(batch_tokens, repr_layers=[33])
                # 提取最后一层的 [CLS] token
                embeddings = results["representations"][33][:, 0, :]
                features.append(embeddings.cpu())
                
        return torch.cat(features)
```

### 3.2 数据集划分策略

```python
class DataSplitter:
    def __init__(self, random_seed=42):
        self.seed = random_seed
        
    def homology_aware_split(self, sequences, labels, clusters):
        """基于同源聚类的数据划分"""
        # 1. 统计每个 cluster 的大小和标签分布
        cluster_stats = pd.DataFrame({
            'cluster_id': clusters,
            'label': labels,
            'seq': sequences
        }).groupby('cluster_id').agg({
            'label': ['mean', 'count'],
            'seq': 'first'
        })
        
        # 2. 分层采样，确保训练/验证/测试集的标签分布一致
        from sklearn.model_selection import StratifiedGroupKFold
        sgkf = StratifiedGroupKFold(n_splits=5, shuffle=True, random_state=self.seed)
        
        # 3. 创建 5-fold，取第一折作为测试集(20%)，其余作为训练集
        # 再从训练集中划分出验证集(20% of 80% = 16%)
        
        return train_data, val_data, test_data
```

## 阶段四：模型训练（第4-5周）

### 4.1 模型架构

```python
class ESM2AMPClassifier(nn.Module):
    def __init__(self, esm_model, hidden_dim=256, dropout=0.3):
        super().__init__()
        self.esm = esm_model
        self.esm_dim = 1280  # ESM-2 650M 的维度
        
        # 冻结 ESM 底层，只微调顶层
        for param in self.esm.parameters():
            param.requires_grad = False
        for param in self.esm.layers[-4:].parameters():  # 解冻最后 4 层
            param.requires_grad = True
            
        # 分类头
        self.classifier = nn.Sequential(
            nn.Linear(self.esm_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
    def forward(self, tokens, repr_layers=[33]):
        # ESM-2 前向传播
        results = self.esm(tokens, repr_layers=repr_layers)
        # 取 [CLS] token (位置 0)
        cls_repr = results["representations"][repr_layers[0]][:, 0, :]
        # 分类
        logits = self.classifier(cls_repr)
        return logits
```

### 4.2 训练配置

```yaml
# config.yaml
training:
  batch_size: 16
  learning_rate: 1e-4
  warmup_steps: 500
  max_epochs: 50
  early_stopping_patience: 5
  gradient_clip_val: 1.0
  
  # 优化器配置
  optimizer:
    type: "AdamW"
    weight_decay: 0.01
    betas: [0.9, 0.999]
    
  # 学习率调度
  scheduler:
    type: "cosine_with_warmup"
    num_warmup_steps: 500
    num_training_steps: 10000
    
  # 损失函数
  loss:
    type: "focal_loss"  # 处理潜在的类别不平衡
    alpha: 0.25
    gamma: 2.0
```

### 4.3 训练脚本

```python
class AMPTrainer:
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    def train_epoch(self, model, dataloader, optimizer, criterion):
        model.train()
        total_loss = 0
        predictions = []
        targets = []
        
        for batch in tqdm(dataloader):
            tokens = batch['tokens'].to(self.device)
            labels = batch['labels'].to(self.device)
            
            # 混合精度训练
            with autocast():
                logits = model(tokens)
                loss = criterion(logits.squeeze(), labels.float())
                
            # 反向传播
            self.scaler.scale(loss).backward()
            self.scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            self.scaler.step(optimizer)
            self.scaler.update()
            optimizer.zero_grad()
            
            # 记录
            total_loss += loss.item()
            predictions.extend(torch.sigmoid(logits).cpu().numpy())
            targets.extend(labels.cpu().numpy())
            
        return total_loss / len(dataloader), predictions, targets
```

## 阶段五：评估与验证（第5-6周）

### 5.1 评估指标体系

```python
class AMPEvaluator:
    def __init__(self):
        self.metrics = {}
        
    def calculate_metrics(self, y_true, y_pred_proba, threshold=0.5):
        """计算全面的评估指标"""
        y_pred = (y_pred_proba >= threshold).astype(int)
        
        metrics = {
            'auroc': roc_auc_score(y_true, y_pred_proba),
            'auprc': average_precision_score(y_true, y_pred_proba),
            'accuracy': accuracy_score(y_true, y_pred),
            'balanced_accuracy': balanced_accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred),
            'recall': recall_score(y_true, y_pred),
            'f1': f1_score(y_true, y_pred),
            'mcc': matthews_corrcoef(y_true, y_pred),
            'specificity': recall_score(y_true, y_pred, pos_label=0)
        }
        
        # 计算最优阈值
        fpr, tpr, thresholds = roc_curve(y_true, y_pred_proba)
        optimal_idx = np.argmax(tpr - fpr)
        optimal_threshold = thresholds[optimal_idx]
        metrics['optimal_threshold'] = optimal_threshold
        
        return metrics
        
    def bootstrap_confidence_interval(self, y_true, y_pred_proba, n_bootstrap=1000):
        """Bootstrap 95% 置信区间"""
        scores = []
        for _ in range(n_bootstrap):
            idx = np.random.choice(len(y_true), len(y_true), replace=True)
            score = roc_auc_score(y_true[idx], y_pred_proba[idx])
            scores.append(score)
        return np.percentile(scores, [2.5, 97.5])
```

### 5.2 外部验证集

```python
external_datasets = {
    'ADAPTABLE': {
        'url': 'https://adaptable.u-bourgogne.fr/download/adaptable_amps.fasta',
        'description': '2023年新发布的AMP数据库'
    },
    'Antimicrobial_Peptide_Scanner': {
        'url': 'https://www.dveltri.com/ascan/v2/download',
        'description': '独立收集的验证集'
    },
    'Clinical_AMPs': {
        'source': 'PubMed 2023-2024 临床试验报道的新AMP'
    }
}
```

## 阶段六：模型解释与生物学验证（第6-7周）

### 6.1 注意力可视化

```python
class AttentionAnalyzer:
    def extract_attention_patterns(self, model, sequence):
        """提取 ESM-2 的注意力模式"""
        tokens = self.tokenize(sequence)
        with torch.no_grad():
            results = model(tokens, need_head_weights=True)
            attention = results["attentions"]  # [layers, heads, seq_len, seq_len]
            
        # 聚合多头注意力
        attention_matrix = attention.mean(dim=1)  # 平均所有注意力头
        
        # 识别关键位点
        importance_scores = attention_matrix.sum(dim=-1)
        top_positions = torch.topk(importance_scores, k=10).indices
        
        return attention_matrix, top_positions
```

### 6.2 生物学验证建议

```python
validation_experiments = {
    'motif_analysis': {
        'tool': 'MEME Suite',
        'method': '从高置信度预测的AMP中提取保守motif'
    },
    'structure_prediction': {
        'tool': 'AlphaFold2 / ESMFold',
        'analysis': '预测二级结构，验证α-helix含量'
    },
    'physicochemical_validation': {
        'properties': ['net_charge', 'hydrophobicity', 'amphipathicity'],
        'expected': '正电荷、两亲性'
    },
    'wet_lab_validation': {
        'assays': ['MIC测定', '溶血性测试', '细胞毒性'],
        'organisms': ['E. coli', 'S. aureus', 'C. albicans']
    }
}
```

## 数据质量检查清单

```python
quality_checks = {
    '序列长度分布': '确保正负样本长度分布相似',
    '氨基酸组成': '检查是否有偏差（如正样本富含K/R）',
    '同源性检查': 'train/test之间序列相似度<30%',
    '标签噪声': '人工抽查100条，验证标注准确性',
    '数据泄露': '确保测试集的物种/家族未在训练集出现',
    '类别平衡': '各fold中正负样本比例一致',
    '批次效应': '不同数据库来源的样本均匀分布'
}
```

## 项目时间线

**第1-2周**：数据收集、初步清洗  
**第3周**：去冗余、特征提取  
**第4周**：模型训练、超参数调优  
**第5周**：全面评估、外部验证  
**第6周**：结果分析、可视化  
**第7周**：论文撰写、代码整理

## 关键依赖安装

```bash
# 创建环境
conda create -n amp_esm2 python=3.9
conda activate amp_esm2

# 核心依赖
pip install torch==2.0.1+cu118 -f https://download.pytorch.org/whl/torch_stable.html
pip install fair-esm  # Facebook ESM
pip install biopython pandas numpy scikit-learn
pip install pytorch-lightning wandb  # 训练框架
pip install seaborn matplotlib  # 可视化

# 生物信息学工具
conda install -c bioconda mmseqs2 cd-hit
```

这个计划确保了：
1. 数据质量可控
2. 避免数据泄露
3. 充分利用 ESM-2 的预训练知识
4. 结果可重复、可解释

需要我详细展开某个部分的代码实现吗？