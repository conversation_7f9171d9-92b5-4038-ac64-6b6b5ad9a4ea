# 抗革兰氏阴性菌高活性抗菌肽数据收集执行指南

## 🚀 立即开始执行

### 第一步：环境准备 (已完成 ✅)

您现在拥有：
- ✅ **详细的25天执行计划** (`docs/data_collection_plan.md` + `docs/data_collection_plan_part2.md`)
- ✅ **标准化数据模板** (`templates/data_collection_template.csv`)
- ✅ **质量控制工具** (`scripts/data_collection/quality_control_tools.py`)
- ✅ **完整的任务管理系统** (6个阶段，详细子任务)

### 第二步：立即开始DBAASP数据收集

#### 🎯 今天的具体任务 (第3天)

**目标**: 收集200-250个E. coli高活性抗菌肽

**操作步骤**:

1. **访问DBAASP**:
   ```
   https://dbaasp.org/search
   ```

2. **设置搜索参数**:
   ```
   Target Organism: Escherichia coli
   Activity Type: Antibacterial
   Peptide Length: 10-50
   MIC Threshold: ≤ 32 μg/mL
   ```

3. **优先收集高活性肽**:
   - 第一轮: MIC ≤ 8 μg/mL (目标100个)
   - 第二轮: MIC 8-16 μg/mL (目标100个)
   - 第三轮: MIC 16-32 μg/mL (目标50个)

4. **数据记录格式**:
   使用提供的CSV模板，每个序列记录：
   - Peptide_ID: GN_AMP_XXX
   - 完整序列
   - 精确MIC值
   - 实验条件
   - 文献PMID

#### 📋 每日工作流程

**上午 (9:00-12:00)**:
- DBAASP搜索和筛选
- 记录50-80个序列
- 实时质量检查

**下午 (14:00-17:00)**:
- 文献验证和补充信息
- 数据清洗和格式化
- 质量评分计算

**晚上 (19:00-21:00)**:
- 数据备份和整理
- 生成日报
- 准备明日计划

#### 🔍 质量控制检查点

**每收集50个序列后**:
```python
# 运行质量检查
python scripts/data_collection/quality_control_tools.py

# 检查指标:
# - 平均质量评分 ≥ 8.0
# - MIC数据完整性 ≥ 95%
# - 序列有效性 ≥ 98%
```

### 第三步：建立每日报告系统

#### 日报模板

```
DBAASP数据收集日报 - 第X天 (E. coli专项)
==========================================

收集统计:
- 新增序列: XXX个
- 累计序列: XXX个  
- 高活性肽(≤16 μg/mL): XXX个 (XX%)
- 平均质量评分: X.X

质量指标:
- 数据完整性: XX%
- 文献可追溯性: XX%
- 序列有效性: XX%

MIC分布:
- ≤4 μg/mL: XX个
- 4-8 μg/mL: XX个
- 8-16 μg/mL: XX个
- 16-32 μg/mL: XX个

问题记录:
- 需要验证的数据: XX个
- 文献无法获取: XX个
- 其他问题: XXX

明日计划:
- 目标菌株: P. aeruginosa
- 预期收集量: 150-200个
- 重点关注: 抗铜绿假单胞菌特异性肽
```

---

## 📊 进度追踪系统

### 总体目标追踪

| 指标 | 目标 | 当前 | 进度 |
|------|------|------|------|
| 总序列数 | 1,000-2,000 | 0 | 0% |
| E. coli序列 | 400-500 | 0 | 0% |
| P. aeruginosa序列 | 300-400 | 0 | 0% |
| A. baumannii序列 | 200-300 | 0 | 0% |
| 高活性序列(≤16 μg/mL) | ≥70% | 0% | - |
| 平均质量评分 | ≥8.0 | 0 | - |

### 每周里程碑

**第1周 (第1-7天)**:
- [ ] 环境搭建完成
- [ ] DBAASP E. coli数据收集 (200-250个)
- [ ] DBAASP P. aeruginosa数据收集 (150-200个)
- [ ] 质量控制流程验证

**第2周 (第8-14天)**:
- [ ] DBAASP数据收集完成 (600-800个)
- [ ] CAMPR4数据收集开始
- [ ] 文献挖掘启动
- [ ] 初步去重处理

**第3周 (第15-21天)**:
- [ ] 文献挖掘完成 (400-600个)
- [ ] 数据整合和去重
- [ ] 质量控制和验证
- [ ] 达到1,000个序列目标

**第4周 (第22-25天)**:
- [ ] 最终数据集构建
- [ ] 全面质量验证
- [ ] 数据集文档完成
- [ ] 准备模型训练

---

## 🛠️ 实用工具和技巧

### DBAASP搜索技巧

**高效搜索策略**:
1. **分批搜索**: 按MIC范围分批，避免遗漏
2. **交叉验证**: 用不同关键词验证结果
3. **文献追踪**: 重点关注高影响因子期刊
4. **实验条件**: 优先选择标准实验方法

**常见问题解决**:
- **搜索结果过多**: 增加筛选条件
- **数据不完整**: 查找原始文献补充
- **MIC单位不统一**: 标准化为μg/mL
- **序列格式问题**: 使用质量控制工具验证

### 文献管理技巧

**高效文献处理**:
1. **批量下载**: 使用机构访问权限
2. **关键信息提取**: 重点关注方法和结果部分
3. **引用管理**: 使用Zotero或Mendeley
4. **质量评估**: 优先处理高影响因子期刊

### 数据质量保证

**实时质量检查**:
```python
# 每日运行质量检查
def daily_quality_check(data_file):
    qc = AMPDataQualityController()
    data = pd.read_csv(data_file)
    
    # 生成报告
    report = qc.generate_quality_report(data)
    
    # 检查关键指标
    if report['quality_stats']['mean_quality_score'] < 8.0:
        print("⚠️ 警告: 平均质量评分低于8.0")
    
    if report['validation_results']['valid_sequences'] / len(data) < 0.95:
        print("⚠️ 警告: 序列验证通过率低于95%")
    
    return report
```

---

## 🎯 成功关键因素

### 数据收集成功要素

1. **系统性**: 严格按照计划执行
2. **质量优先**: 宁缺毋滥，确保数据质量
3. **及时验证**: 每日质量检查，及时发现问题
4. **文档完整**: 详细记录每个数据来源
5. **持续改进**: 根据发现的问题调整策略

### 常见陷阱避免

1. **数据重复**: 建立严格的去重机制
2. **质量下降**: 不要为了数量牺牲质量
3. **文献缺失**: 确保每个序列都有可追溯来源
4. **格式不统一**: 使用标准化模板
5. **进度拖延**: 严格按照时间计划执行

---

## 📞 支持和帮助

### 遇到问题时

1. **技术问题**: 检查质量控制工具输出
2. **数据问题**: 参考数据质量标准
3. **进度问题**: 调整每日目标，保持总体进度
4. **质量问题**: 提高筛选标准，减少收集量

### 联系方式

- **项目负责人**: ZK
- **邮箱**: <EMAIL>
- **紧急情况**: 立即停止收集，分析问题原因

---

## 🎉 开始执行！

**您现在拥有了完整的执行框架，可以立即开始数据收集工作！**

**第一步**: 打开 https://dbaasp.org/search
**第二步**: 按照指南开始收集E. coli数据
**第三步**: 使用模板记录每个序列
**第四步**: 每日运行质量检查
**第五步**: 生成日报并调整策略

**预祝数据收集成功！** 🚀
