{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 抗菌肽数据清洗与预处理 (第2-3周)\n", "\n", "**作者**: ZK  \n", "**邮箱**: <EMAIL>  \n", "**日期**: 2025-07-20  \n", "**描述**: 对下载的抗菌肽数据进行清洗、标准化和去冗余处理\n", "\n", "## 目标\n", "1. 序列清洗和标准化\n", "2. 去除冗余序列\n", "3. 负样本构建\n", "4. 数据质量验证"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境设置和依赖检查"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "from Bio import SeqIO\n", "from Bio.Seq import Seq\n", "from Bio.SeqRecord import SeqRecord\n", "import subprocess\n", "import requests\n", "from datetime import datetime\n", "import json\n", "import re\n", "from collections import Counter\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from tqdm import tqdm\n", "import hashlib\n", "import random\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"✅ 库导入完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 路径配置\n", "PROJECT_ROOT = os.path.abspath(os.path.join(os.getcwd(), '..', '..'))\n", "DATA_RAW_DIR = os.path.join(PROJECT_ROOT, 'data', 'raw')\n", "DATA_PROCESSED_DIR = os.path.join(PROJECT_ROOT, 'data', 'processed')\n", "DATA_ANALYSIS_READY_DIR = os.path.join(PROJECT_ROOT, 'data', 'analysis_ready')\n", "\n", "# 创建目录\n", "os.makedirs(DATA_PROCESSED_DIR, exist_ok=True)\n", "os.makedirs(DATA_ANALYSIS_READY_DIR, exist_ok=True)\n", "\n", "print(f\"原始数据目录: {DATA_RAW_DIR}\")\n", "print(f\"处理数据目录: {DATA_PROCESSED_DIR}\")\n", "print(f\"分析就绪目录: {DATA_ANALYSIS_READY_DIR}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 序列清洗工具类"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SequenceCleaner:\n", "    \"\"\"序列清洗器\"\"\"\n", "    \n", "    def __init__(self, min_length=10, max_length=200, max_unknown_ratio=0.1):\n", "        self.min_length = min_length\n", "        self.max_length = max_length\n", "        self.max_unknown_ratio = max_unknown_ratio\n", "        self.valid_aa = set('ACDEFGHIKLMNPQRSTVWY')\n", "        \n", "        # 异常氨基酸替换规则\n", "        self.aa_replacements = {\n", "            'B': 'N',  # Asn or Asp\n", "            'Z': 'Q',  # Gln or Glu\n", "            'U': 'C',  # Selenocysteine -> <PERSON><PERSON><PERSON>\n", "            'O': 'K',  # Pyrrolysine -> Lysine\n", "            'J': 'L'   # Leucine or Isoleucine -> <PERSON><PERSON>ine\n", "        }\n", "        \n", "        self.cleaning_stats = {\n", "            'total_input': 0,\n", "            'too_short': 0,\n", "            'too_long': 0,\n", "            'too_many_unknown': 0,\n", "            'invalid_characters': 0,\n", "            'duplicates_removed': 0,\n", "            'final_count': 0\n", "        }\n", "    \n", "    def clean_sequence(self, seq_str):\n", "        \"\"\"清洗单条序列\"\"\"\n", "        # 转换为大写\n", "        seq_str = seq_str.upper().strip()\n", "        \n", "        # 移除非字母字符\n", "        seq_str = re.sub(r'[^A-Z]', '', seq_str)\n", "        \n", "        # 替换异常氨基酸\n", "        for old, new in self.aa_replacements.items():\n", "            seq_str = seq_str.replace(old, new)\n", "        \n", "        return seq_str\n", "    \n", "    def is_valid_sequence(self, seq_str):\n", "        \"\"\"检查序列是否有效\"\"\"\n", "        # 长度检查\n", "        if len(seq_str) < self.min_length:\n", "            self.cleaning_stats['too_short'] += 1\n", "            return False, \"too_short\"\n", "        \n", "        if len(seq_str) > self.max_length:\n", "            self.cleaning_stats['too_long'] += 1\n", "            return False, \"too_long\"\n", "        \n", "        # 检查未知氨基酸比例\n", "        unknown_count = seq_str.count('X')\n", "        unknown_ratio = unknown_count / len(seq_str)\n", "        \n", "        if unknown_ratio > self.max_unknown_ratio:\n", "            self.cleaning_stats['too_many_unknown'] += 1\n", "            return False, \"too_many_unknown\"\n", "        \n", "        # 检查是否包含无效字符\n", "        for char in seq_str:\n", "            if char not in self.valid_aa and char != 'X':\n", "                self.cleaning_stats['invalid_characters'] += 1\n", "                return False, \"invalid_characters\"\n", "        \n", "        return True, \"valid\"\n", "    \n", "    def clean_fasta_file(self, input_file, output_file):\n", "        \"\"\"清洗FASTA文件\"\"\"\n", "        cleaned_sequences = []\n", "        seen_sequences = set()\n", "        \n", "        print(f\"正在清洗文件: {os.path.basename(input_file)}\")\n", "        \n", "        try:\n", "            records = list(SeqIO.parse(input_file, \"fasta\"))\n", "            self.cleaning_stats['total_input'] = len(records)\n", "            \n", "            for i, record in enumerate(tqdm(records, desc=\"清洗序列\")):\n", "                # 清洗序列\n", "                cleaned_seq = self.clean_sequence(str(record.seq))\n", "                \n", "                # 验证序列\n", "                is_valid, reason = self.is_valid_sequence(cleaned_seq)\n", "                \n", "                if is_valid:\n", "                    # 检查重复\n", "                    if cleaned_seq not in seen_sequences:\n", "                        seen_sequences.add(cleaned_seq)\n", "                        \n", "                        # 创建新的记录\n", "                        new_record = SeqRecord(\n", "                            Seq(cleaned_seq),\n", "                            id=f\"seq_{len(cleaned_sequences)+1:06d}\",\n", "                            description=f\"cleaned_from_{record.id}\"\n", "                        )\n", "                        cleaned_sequences.append(new_record)\n", "                    else:\n", "                        self.cleaning_stats['duplicates_removed'] += 1\n", "            \n", "            self.cleaning_stats['final_count'] = len(cleaned_sequences)\n", "            \n", "            # 保存清洗后的序列\n", "            SeqIO.write(cleaned_sequences, output_file, \"fasta\")\n", "            \n", "            print(f\"✅ 清洗完成，保存到: {output_file}\")\n", "            return True\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ 清洗过程中出错: {e}\")\n", "            return False\n", "    \n", "    def print_cleaning_stats(self):\n", "        \"\"\"打印清洗统计信息\"\"\"\n", "        print(\"\\n📊 序列清洗统计:\")\n", "        print(f\"  输入序列总数: {self.cleaning_stats['total_input']:,}\")\n", "        print(f\"  过短序列 (<{self.min_length}): {self.cleaning_stats['too_short']:,}\")\n", "        print(f\"  过长序列 (>{self.max_length}): {self.cleaning_stats['too_long']:,}\")\n", "        print(f\"  未知氨基酸过多: {self.cleaning_stats['too_many_unknown']:,}\")\n", "        print(f\"  包含无效字符: {self.cleaning_stats['invalid_characters']:,}\")\n", "        print(f\"  重复序列移除: {self.cleaning_stats['duplicates_removed']:,}\")\n", "        print(f\"  最终有效序列: {self.cleaning_stats['final_count']:,}\")\n", "        \n", "        if self.cleaning_stats['total_input'] > 0:\n", "            retention_rate = (self.cleaning_stats['final_count'] / self.cleaning_stats['total_input']) * 100\n", "            print(f\"  保留率: {retention_rate:.1f}%\")\n", "\n", "print(\"✅ SequenceCleaner类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 负样本构建器"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class NegativeSampleBuilder:\n", "    \"\"\"负样本构建器\"\"\"\n", "    \n", "    def __init__(self, output_dir):\n", "        self.output_dir = output_dir\n", "        self.uniprot_base_url = \"https://rest.uniprot.org/uniprotkb/stream\"\n", "    \n", "    def download_uniprot_negatives(self, max_sequences=5000):\n", "        \"\"\"从UniProt下载负样本\"\"\"\n", "        print(\"正在从UniProt下载负样本...\")\n", "        \n", "        # 构建查询参数\n", "        query = (\n", "            \"reviewed:true AND \"\n", "            \"length:[10 TO 200] AND \"\n", "            \"NOT annotation:(antimicrobial OR antibiotic OR antifungal OR \"\n", "            \"antiviral OR defensin OR bacteriocin OR toxin)\"\n", "        )\n", "        \n", "        params = {\n", "            'query': query,\n", "            'format': 'fasta',\n", "            'size': min(max_sequences, 500)  # UniProt限制单次请求数量\n", "        }\n", "        \n", "        try:\n", "            response = requests.get(self.uniprot_base_url, params=params, timeout=60)\n", "            response.raise_for_status()\n", "            \n", "            # 保存到文件\n", "            output_file = os.path.join(self.output_dir, f\"uniprot_negatives_{datetime.now().strftime('%Y%m%d')}.fasta\")\n", "            with open(output_file, 'w') as f:\n", "                f.write(response.text)\n", "            \n", "            print(f\"✅ UniProt负样本下载完成: {output_file}\")\n", "            return output_file\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ UniProt下载失败: {e}\")\n", "            return None\n", "    \n", "    def generate_synthetic_negatives(self, positive_sequences, num_synthetic=1000):\n", "        \"\"\"生成合成负样本\"\"\"\n", "        print(f\"正在生成 {num_synthetic} 个合成负样本...\")\n", "        \n", "        synthetic_sequences = []\n", "        \n", "        for i in range(num_synthetic):\n", "            if i < len(positive_sequences):\n", "                # 选择一个正样本作为模板\n", "                template = positive_sequences[i % len(positive_sequences)]\n", "                \n", "                # 随机选择生成方法\n", "                method = random.choice(['shuffle', 'reverse', 'random_composition'])\n", "                \n", "                if method == 'shuffle':\n", "                    # 打乱氨基酸顺序但保持组成\n", "                    aa_list = list(template)\n", "                    random.shuffle(aa_list)\n", "                    synthetic_seq = ''.join(aa_list)\n", "                    \n", "                elif method == 'reverse':\n", "                    # 反转序列\n", "                    synthetic_seq = template[::-1]\n", "                    \n", "                else:  # random_composition\n", "                    # 基于氨基酸组成随机生成\n", "                    aa_counts = Counter(template)\n", "                    aa_pool = []\n", "                    for aa, count in aa_counts.items():\n", "                        aa_pool.extend([aa] * count)\n", "                    random.shuffle(aa_pool)\n", "                    synthetic_seq = ''.join(aa_pool)\n", "                \n", "                # 创建序列记录\n", "                record = SeqRecord(\n", "                    Seq(synthetic_seq),\n", "                    id=f\"synthetic_neg_{i+1:06d}\",\n", "                    description=f\"synthetic_negative_method_{method}\"\n", "                )\n", "                synthetic_sequences.append(record)\n", "        \n", "        # 保存合成负样本\n", "        output_file = os.path.join(self.output_dir, f\"synthetic_negatives_{datetime.now().strftime('%Y%m%d')}.fasta\")\n", "        SeqIO.write(synthetic_sequences, output_file, \"fasta\")\n", "        \n", "        print(f\"✅ 合成负样本生成完成: {output_file}\")\n", "        return output_file\n", "\n", "print(\"✅ NegativeSampleBuilder类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 执行数据清洗"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查找原始数据文件\n", "raw_files = [f for f in os.listdir(DATA_RAW_DIR) if f.endswith('.fasta')]\n", "print(f\"发现 {len(raw_files)} 个FASTA文件:\")\n", "for f in raw_files:\n", "    print(f\"  - {f}\")\n", "\n", "if not raw_files:\n", "    print(\"❌ 未找到原始数据文件，请先运行数据下载notebook\")\n", "else:\n", "    print(\"\\n开始清洗处理...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化清洗器\n", "cleaner = SequenceCleaner(min_length=10, max_length=200, max_unknown_ratio=0.1)\n", "\n", "# 清洗每个文件\n", "cleaned_files = []\n", "all_positive_sequences = []\n", "\n", "for raw_file in raw_files:\n", "    input_path = os.path.join(DATA_RAW_DIR, raw_file)\n", "    output_filename = f\"cleaned_{raw_file}\"\n", "    output_path = os.path.join(DATA_PROCESSED_DIR, output_filename)\n", "    \n", "    print(f\"\\n处理文件: {raw_file}\")\n", "    print(\"-\" * 40)\n", "    \n", "    success = cleaner.clean_fasta_file(input_path, output_path)\n", "    \n", "    if success:\n", "        cleaned_files.append(output_path)\n", "        cleaner.print_cleaning_stats()\n", "        \n", "        # 收集正样本序列用于负样本生成\n", "        for record in SeqIO.parse(output_path, \"fasta\"):\n", "            all_positive_sequences.append(str(record.seq))\n", "    \n", "    # 重置统计信息\n", "    cleaner.cleaning_stats = {key: 0 for key in cleaner.cleaning_stats}\n", "\n", "print(f\"\\n✅ 清洗完成，共处理 {len(cleaned_files)} 个文件\")\n", "print(f\"收集到 {len(all_positive_sequences)} 个正样本序列\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 生成负样本"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化负样本构建器\n", "neg_builder = NegativeSampleBuilder(DATA_PROCESSED_DIR)\n", "\n", "print(\"开始构建负样本数据集...\")\n", "print(\"=\" * 40)\n", "\n", "# 1. 下载UniProt负样本\n", "uniprot_file = neg_builder.download_uniprot_negatives(max_sequences=2000)\n", "\n", "# 2. 生成合成负样本\n", "if all_positive_sequences:\n", "    synthetic_file = neg_builder.generate_synthetic_negatives(\n", "        all_positive_sequences, \n", "        num_synthetic=min(1000, len(all_positive_sequences))\n", "    )\n", "else:\n", "    print(\"⚠️ 没有正样本序列，跳过合成负样本生成\")\n", "    synthetic_file = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 数据质量验证和可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_sequence_properties(sequences, label):\n", "    \"\"\"分析序列属性\"\"\"\n", "    lengths = [len(seq) for seq in sequences]\n", "    \n", "    # 氨基酸组成分析\n", "    aa_counts = Counter()\n", "    for seq in sequences:\n", "        aa_counts.update(seq)\n", "    \n", "    # 计算理化性质\n", "    hydrophobic_aa = set('AILMFPWV')\n", "    charged_aa = set('DEKR')\n", "    \n", "    hydrophobic_ratios = []\n", "    charged_ratios = []\n", "    \n", "    for seq in sequences:\n", "        hydrophobic_count = sum(1 for aa in seq if aa in hydrophobic_aa)\n", "        charged_count = sum(1 for aa in seq if aa in charged_aa)\n", "        \n", "        hydrophobic_ratios.append(hydrophobic_count / len(seq))\n", "        charged_ratios.append(charged_count / len(seq))\n", "    \n", "    return {\n", "        'label': label,\n", "        'count': len(sequences),\n", "        'lengths': lengths,\n", "        'aa_composition': aa_counts,\n", "        'hydrophobic_ratios': hydrophobic_ratios,\n", "        'charged_ratios': charged_ratios\n", "    }\n", "\n", "# 分析正样本\n", "positive_analysis = analyze_sequence_properties(all_positive_sequences, \"正样本(AMP)\")\n", "\n", "# 分析负样本\n", "negative_sequences = []\n", "if uniprot_file and os.path.exists(uniprot_file):\n", "    for record in SeqIO.parse(uniprot_file, \"fasta\"):\n", "        negative_sequences.append(str(record.seq))\n", "\n", "if synthetic_file and os.path.exists(synthetic_file):\n", "    for record in SeqIO.parse(synthetic_file, \"fasta\"):\n", "        negative_sequences.append(str(record.seq))\n", "\n", "if negative_sequences:\n", "    negative_analysis = analyze_sequence_properties(negative_sequences, \"负样本(非AMP)\")\n", "else:\n", "    negative_analysis = None\n", "\n", "print(f\"正样本数量: {positive_analysis['count']:,}\")\n", "if negative_analysis:\n", "    print(f\"负样本数量: {negative_analysis['count']:,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化序列长度分布\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "fig.suptitle('序列属性分析', fontsize=16, fontweight='bold')\n", "\n", "# 长度分布\n", "axes[0, 0].hist(positive_analysis['lengths'], bins=30, alpha=0.7, label='正样本', color='red')\n", "if negative_analysis:\n", "    axes[0, 0].hist(negative_analysis['lengths'], bins=30, alpha=0.7, label='负样本', color='blue')\n", "axes[0, 0].set_xlabel('序列长度')\n", "axes[0, 0].set_ylabel('频次')\n", "axes[0, 0].set_title('序列长度分布')\n", "axes[0, 0].legend()\n", "\n", "# 疏水性氨基酸比例\n", "axes[0, 1].hist(positive_analysis['hydrophobic_ratios'], bins=20, alpha=0.7, label='正样本', color='red')\n", "if negative_analysis:\n", "    axes[0, 1].hist(negative_analysis['hydrophobic_ratios'], bins=20, alpha=0.7, label='负样本', color='blue')\n", "axes[0, 1].set_xlabel('疏水性氨基酸比例')\n", "axes[0, 1].set_ylabel('频次')\n", "axes[0, 1].set_title('疏水性氨基酸比例分布')\n", "axes[0, 1].legend()\n", "\n", "# 带电氨基酸比例\n", "axes[1, 0].hist(positive_analysis['charged_ratios'], bins=20, alpha=0.7, label='正样本', color='red')\n", "if negative_analysis:\n", "    axes[1, 0].hist(negative_analysis['charged_ratios'], bins=20, alpha=0.7, label='负样本', color='blue')\n", "axes[1, 0].set_xlabel('带电氨基酸比例')\n", "axes[1, 0].set_ylabel('频次')\n", "axes[1, 0].set_title('带电氨基酸比例分布')\n", "axes[1, 0].legend()\n", "\n", "# 氨基酸组成对比\n", "if negative_analysis:\n", "    # 计算氨基酸频率\n", "    pos_total = sum(positive_analysis['aa_composition'].values())\n", "    neg_total = sum(negative_analysis['aa_composition'].values())\n", "    \n", "    aa_list = sorted(set(positive_analysis['aa_composition'].keys()) | \n", "                    set(negative_analysis['aa_composition'].keys()))\n", "    \n", "    pos_freqs = [positive_analysis['aa_composition'].get(aa, 0) / pos_total for aa in aa_list]\n", "    neg_freqs = [negative_analysis['aa_composition'].get(aa, 0) / neg_total for aa in aa_list]\n", "    \n", "    x = np.arange(len(aa_list))\n", "    width = 0.35\n", "    \n", "    axes[1, 1].bar(x - width/2, pos_freqs, width, label='正样本', color='red', alpha=0.7)\n", "    axes[1, 1].bar(x + width/2, neg_freqs, width, label='负样本', color='blue', alpha=0.7)\n", "    axes[1, 1].set_xlabel('氨基酸')\n", "    axes[1, 1].set_ylabel('频率')\n", "    axes[1, 1].set_title('氨基酸组成对比')\n", "    axes[1, 1].set_xticks(x)\n", "    axes[1, 1].set_xticklabels(aa_list)\n", "    axes[1, 1].legend()\n", "    axes[1, 1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.savefig(os.path.join(DATA_PROCESSED_DIR, 'sequence_analysis.png'), dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"\\n✅ 数据分析图表已保存\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 保存处理总结"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成处理总结报告\n", "summary_report = {\n", "    'processing_date': datetime.now().isoformat(),\n", "    'positive_samples': {\n", "        'total_count': positive_analysis['count'],\n", "        'mean_length': np.mean(positive_analysis['lengths']),\n", "        'std_length': np.std(positive_analysis['lengths']),\n", "        'min_length': min(positive_analysis['lengths']),\n", "        'max_length': max(positive_analysis['lengths'])\n", "    },\n", "    'negative_samples': {\n", "        'total_count': negative_analysis['count'] if negative_analysis else 0,\n", "        'mean_length': np.mean(negative_analysis['lengths']) if negative_analysis else 0,\n", "        'std_length': np.std(negative_analysis['lengths']) if negative_analysis else 0,\n", "        'min_length': min(negative_analysis['lengths']) if negative_analysis else 0,\n", "        'max_length': max(negative_analysis['lengths']) if negative_analysis else 0\n", "    },\n", "    'files_processed': {\n", "        'input_files': raw_files,\n", "        'cleaned_files': [os.path.basename(f) for f in cleaned_files],\n", "        'negative_files': []\n", "    }\n", "}\n", "\n", "if uniprot_file:\n", "    summary_report['files_processed']['negative_files'].append(os.path.basename(uniprot_file))\n", "if synthetic_file:\n", "    summary_report['files_processed']['negative_files'].append(os.path.basename(synthetic_file))\n", "\n", "# 保存总结报告\n", "summary_file = os.path.join(DATA_PROCESSED_DIR, f'processing_summary_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.json')\n", "with open(summary_file, 'w', encoding='utf-8') as f:\n", "    json.dump(summary_report, f, ensure_ascii=False, indent=2)\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"📋 数据清洗与预处理总结\")\n", "print(\"=\" * 50)\n", "print(f\"✅ 正样本数量: {summary_report['positive_samples']['total_count']:,}\")\n", "print(f\"✅ 负样本数量: {summary_report['negative_samples']['total_count']:,}\")\n", "print(f\"📁 处理文件数: {len(cleaned_files)}\")\n", "print(f\"📊 总结报告: {summary_file}\")\n", "print(f\"📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"\\n📝 下一步: 运行 03_特征提取与数据集构建.ipynb\")"]}], "metadata": {"kernelspec": {"display_name": "AMP ESM-2", "language": "python", "name": "amp_esm2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 4}