# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-20
# 描述: 修复编码问题并生成最终的训练适用性分析

import os
import pandas as pd
import numpy as np
from Bio import SeqIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord
from datetime import datetime
import json
import re
from collections import Counter
import chardet

class EncodingFixerAndAnalyzer:
    """修复编码问题并分析序列"""
    
    def __init__(self, data_dir):
        self.data_dir = data_dir
        self.analysis_results = {}
        
    def detect_and_fix_encoding(self, filepath):
        """检测并修复文件编码"""
        try:
            # 检测编码
            with open(filepath, 'rb') as f:
                raw_data = f.read()
                encoding_result = chardet.detect(raw_data)
                detected_encoding = encoding_result['encoding']
                confidence = encoding_result['confidence']
            
            print(f"文件: {os.path.basename(filepath)}")
            print(f"检测到编码: {detected_encoding} (置信度: {confidence:.2f})")
            
            # 尝试读取文件
            encodings_to_try = [detected_encoding, 'utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            
            for encoding in encodings_to_try:
                if encoding is None:
                    continue
                try:
                    with open(filepath, 'r', encoding=encoding) as f:
                        content = f.read()
                    
                    # 检查是否是有效的FASTA格式
                    if content.startswith('>') or '>' in content[:1000]:
                        print(f"✅ 成功使用编码: {encoding}")
                        return content, encoding
                        
                except (UnicodeDecodeError, UnicodeError):
                    continue
            
            print(f"❌ 无法解码文件")
            return None, None
            
        except Exception as e:
            print(f"❌ 编码检测失败: {e}")
            return None, None
    
    def analyze_sequences_from_content(self, content, filename):
        """从文件内容分析序列"""
        try:
            # 将内容写入临时文件进行解析
            temp_file = filename + '.temp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            sequences = []
            sequence_lengths = []
            invalid_sequences = 0
            
            # 解析FASTA
            for record in SeqIO.parse(temp_file, "fasta"):
                seq_str = str(record.seq).upper()
                sequences.append(seq_str)
                sequence_lengths.append(len(seq_str))
                
                # 检查无效字符
                if not re.match(r'^[ACDEFGHIKLMNPQRSTVWYXBZ]+$', seq_str):
                    invalid_sequences += 1
            
            # 删除临时文件
            os.remove(temp_file)
            
            # 计算统计信息
            stats = {
                'filename': os.path.basename(filename),
                'total_sequences': len(sequences),
                'valid_sequences': len(sequences) - invalid_sequences,
                'invalid_sequences': invalid_sequences,
                'min_length': min(sequence_lengths) if sequence_lengths else 0,
                'max_length': max(sequence_lengths) if sequence_lengths else 0,
                'mean_length': np.mean(sequence_lengths) if sequence_lengths else 0,
                'median_length': np.median(sequence_lengths) if sequence_lengths else 0,
                'sequences_under_50': sum(1 for l in sequence_lengths if l < 50),
                'sequences_under_100': sum(1 for l in sequence_lengths if l < 100),
                'sequences_over_100': sum(1 for l in sequence_lengths if l > 100),
            }
            
            # 计算适合训练的序列
            training_suitable = 0
            for i, seq in enumerate(sequences):
                length = sequence_lengths[i]
                if (10 <= length <= 100 and 
                    re.match(r'^[ACDEFGHIKLMNPQRSTVWY]+$', seq)):
                    training_suitable += 1
            
            stats['training_suitable'] = training_suitable
            stats['training_ratio'] = training_suitable / len(sequences) if sequences else 0
            
            return stats
            
        except Exception as e:
            print(f"❌ 序列分析失败: {e}")
            return None
    
    def process_all_fasta_files(self):
        """处理所有FASTA文件"""
        print("🔧 修复编码问题并分析所有FASTA文件...")
        print("=" * 60)
        
        fasta_files = [f for f in os.listdir(self.data_dir) if f.endswith('.fasta')]
        
        for filename in sorted(fasta_files):
            filepath = os.path.join(self.data_dir, filename)
            print(f"\n📁 处理文件: {filename}")
            
            # 检测并修复编码
            content, encoding = self.detect_and_fix_encoding(filepath)
            
            if content:
                # 分析序列
                stats = self.analyze_sequences_from_content(content, filepath)
                
                if stats:
                    self.analysis_results[filename] = stats
                    print(f"📊 分析结果:")
                    print(f"   总序列数: {stats['total_sequences']:,}")
                    print(f"   适合训练: {stats['training_suitable']:,} ({stats['training_ratio']*100:.1f}%)")
                    print(f"   长度范围: {stats['min_length']}-{stats['max_length']} (平均: {stats['mean_length']:.1f})")
            
            print("-" * 40)
    
    def generate_comprehensive_report(self):
        """生成全面的分析报告"""
        if not self.analysis_results:
            print("❌ 没有可分析的数据")
            return
        
        print("\n" + "=" * 70)
        print("📊 完整数据集训练适用性分析报告")
        print("=" * 70)
        
        # 汇总统计
        total_files = len(self.analysis_results)
        total_sequences = sum(stats['total_sequences'] for stats in self.analysis_results.values())
        total_valid = sum(stats['valid_sequences'] for stats in self.analysis_results.values())
        total_training_suitable = sum(stats['training_suitable'] for stats in self.analysis_results.values())
        
        print(f"📋 总体统计:")
        print(f"   数据文件数: {total_files}")
        print(f"   总序列数: {total_sequences:,}")
        print(f"   有效序列: {total_valid:,} ({total_valid/total_sequences*100:.1f}%)")
        print(f"   适合训练: {total_training_suitable:,} ({total_training_suitable/total_sequences*100:.1f}%)")
        
        # 按文件详细统计
        print(f"\n📋 各文件训练适用性详情:")
        print(f"{'文件名':<35} {'总数':<8} {'适合训练':<10} {'比例':<8} {'推荐':<6}")
        print("-" * 75)
        
        high_quality_files = []
        medium_quality_files = []
        low_quality_files = []
        
        for filename, stats in sorted(self.analysis_results.items()):
            ratio = stats['training_ratio'] * 100
            
            if ratio > 80:
                recommendation = "✅"
                high_quality_files.append(filename)
            elif ratio > 50:
                recommendation = "⚠️"
                medium_quality_files.append(filename)
            else:
                recommendation = "❌"
                low_quality_files.append(filename)
            
            print(f"{filename:<35} {stats['total_sequences']:<8,} {stats['training_suitable']:<10,} {ratio:<7.1f}% {recommendation:<6}")
        
        # 数据质量分类
        print(f"\n💡 数据质量分类:")
        print(f"   ✅ 高质量文件 (>80%适用): {len(high_quality_files)}个")
        print(f"   ⚠️ 中等质量文件 (50-80%适用): {len(medium_quality_files)}个")
        print(f"   ❌ 低质量文件 (<50%适用): {len(low_quality_files)}个")
        
        # 长度分布
        under_50_total = sum(stats['sequences_under_50'] for stats in self.analysis_results.values())
        under_100_total = sum(stats['sequences_under_100'] for stats in self.analysis_results.values())
        over_100_total = sum(stats['sequences_over_100'] for stats in self.analysis_results.values())
        
        print(f"\n📏 序列长度分布:")
        print(f"   <50 AA:  {under_50_total:,} ({under_50_total/total_sequences*100:.1f}%)")
        print(f"   <100 AA: {under_100_total:,} ({under_100_total/total_sequences*100:.1f}%)")
        print(f"   >100 AA: {over_100_total:,} ({over_100_total/total_sequences*100:.1f}%)")
        
        # 与参考研究对比
        print(f"\n🔍 与参考研究对比:")
        print(f"   参考研究标准: 24,766个AMP序列 (83%<50 AA)")
        print(f"   我们的数据集: {total_training_suitable:,}个适合训练的序列")
        print(f"   长度<50 AA比例: {under_50_total/total_sequences*100:.1f}%")
        
        if total_training_suitable >= 24766:
            print(f"   ✅ 数量充足: 超过参考标准 {(total_training_suitable/24766-1)*100:.1f}%")
        else:
            shortage = 24766 - total_training_suitable
            print(f"   ⚠️ 数量不足: 还需要 {shortage:,} 个序列")
        
        # 训练建议
        print(f"\n🎯 模型训练建议:")
        
        if total_training_suitable >= 30000:
            print(f"   ✅ 数据量充足，可以直接开始训练")
            print(f"   ✅ 建议使用高质量文件作为主要训练数据")
            print(f"   ✅ 可以进行数据增强和交叉验证")
        elif total_training_suitable >= 20000:
            print(f"   ⚠️ 数据量基本充足，建议补充更多数据")
            print(f"   ⚠️ 重点使用高质量文件")
            print(f"   ⚠️ 考虑数据增强技术")
        else:
            print(f"   ❌ 数据量不足，强烈建议补充APD和CAMPR4数据")
            print(f"   ❌ 优先获取高质量数据源")
        
        # 数据预处理建议
        print(f"\n🔧 数据预处理建议:")
        print(f"   1. 去重: 基于70-80%序列相似度")
        print(f"   2. 长度筛选: 保留10-100个氨基酸的序列")
        print(f"   3. 质量过滤: 移除包含无效字符的序列")
        print(f"   4. 平衡采样: 确保不同来源数据的平衡")
        
        return {
            'total_sequences': total_sequences,
            'training_suitable': total_training_suitable,
            'high_quality_files': high_quality_files,
            'data_sufficient': total_training_suitable >= 24766,
            'recommendation': 'ready' if total_training_suitable >= 30000 else 'need_more'
        }
    
    def save_final_analysis(self):
        """保存最终分析结果"""
        output_file = os.path.join(self.data_dir, f'final_training_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        analysis_data = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_files_analyzed': len(self.analysis_results),
            'file_details': self.analysis_results,
            'summary': {
                'total_sequences': sum(stats['total_sequences'] for stats in self.analysis_results.values()),
                'training_suitable_sequences': sum(stats['training_suitable'] for stats in self.analysis_results.values()),
                'average_training_ratio': np.mean([stats['training_ratio'] for stats in self.analysis_results.values()]),
                'data_quality_assessment': 'sufficient' if sum(stats['training_suitable'] for stats in self.analysis_results.values()) >= 24766 else 'insufficient'
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 最终分析结果已保存到: {output_file}")

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    data_dir = os.path.join(project_root, 'data', 'raw')
    
    # 创建分析器
    analyzer = EncodingFixerAndAnalyzer(data_dir)
    
    # 处理所有文件
    analyzer.process_all_fasta_files()
    
    # 生成报告
    report = analyzer.generate_comprehensive_report()
    
    # 保存结果
    analyzer.save_final_analysis()
    
    print(f"\n🎉 完整分析完成！")
    
    if report and report.get('data_sufficient'):
        print(f"✅ 数据充足，可以开始模型训练")
        print(f"📝 下一步: 运行 02_数据清洗与预处理.ipynb")
    else:
        print(f"⚠️ 建议补充APD和CAMPR4数据以达到最佳效果")

if __name__ == "__main__":
    main()
