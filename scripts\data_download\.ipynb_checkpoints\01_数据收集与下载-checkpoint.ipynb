{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 抗菌肽数据收集与下载 (第1-2周)\n", "\n", "**作者**: ZK  \n", "**邮箱**: <EMAIL>  \n", "**日期**: 2025-07-20  \n", "**描述**: 从多个公开数据库下载抗菌肽数据，包括正样本和负样本的收集\n", "\n", "## 目标\n", "1. 从5个主要数据库下载正样本AMP数据\n", "2. 构建三重负样本策略\n", "3. 初步数据质量检查\n", "4. 记录数据来源和统计信息"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境检查与依赖安装"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python版本: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]\n", "操作系统: Windows 10\n", "架构: AMD64\n"]}], "source": ["# 检查Python版本和基础环境\n", "import sys\n", "import platform\n", "print(f\"Python版本: {sys.version}\")\n", "print(f\"操作系统: {platform.system()} {platform.release()}\")\n", "print(f\"架构: {platform.machine()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装必要的依赖包\n", "import subprocess\n", "import sys\n", "\n", "def install_package(package):\n", "    \"\"\"安装Python包的辅助函数\"\"\"\n", "    try:\n", "        subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", package])\n", "        print(f\"✅ 成功安装 {package}\")\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"❌ 安装 {package} 失败: {e}\")\n", "\n", "# 核心依赖包列表\n", "required_packages = [\n", "    \"biopython>=1.81\",\n", "    \"pandas>=1.5.0\",\n", "    \"numpy>=1.24.0\",\n", "    \"requests>=2.28.0\",\n", "    \"tqdm>=4.64.0\",\n", "    \"matplotlib>=3.6.0\",\n", "    \"seaborn>=0.12.0\"\n", "]\n", "\n", "print(\"开始安装依赖包...\")\n", "for package in required_packages:\n", "    install_package(package)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import requests\n", "import pandas as pd\n", "import numpy as np\n", "from Bio import SeqIO\n", "from Bio.Seq import Seq\n", "from Bio.SeqRecord import SeqRecord\n", "from datetime import datetime\n", "import hashlib\n", "from tqdm import tqdm\n", "import time\n", "import json\n", "\n", "print(\"✅ 所有库导入成功\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 配置和路径设置"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置项目路径\n", "PROJECT_ROOT = os.path.abspath(os.path.join(os.getcwd(), '..', '..'))\n", "DATA_RAW_DIR = os.path.join(PROJECT_ROOT, 'data', 'raw')\n", "DATA_PROCESSED_DIR = os.path.join(PROJECT_ROOT, 'data', 'processed')\n", "\n", "# 创建必要的目录\n", "os.makedirs(DATA_RAW_DIR, exist_ok=True)\n", "os.makedirs(DATA_PROCESSED_DIR, exist_ok=True)\n", "\n", "print(f\"项目根目录: {PROJECT_ROOT}\")\n", "print(f\"原始数据目录: {DATA_RAW_DIR}\")\n", "print(f\"处理数据目录: {DATA_PROCESSED_DIR}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据库配置\n", "DATABASE_CONFIG = {\n", "    'dbAMP': {\n", "        'url': 'http://csb.cse.yzu.edu.tw/dbAMP/download/dbAMP_pos.fasta',\n", "        'backup_url': 'https://github.com/batriste/dbAMP/raw/main/data/dbAMP_pos.fasta',\n", "        'description': 'dbAMP正样本数据库',\n", "        'expected_format': 'fasta'\n", "    },\n", "    'DRAMP': {\n", "        'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php',\n", "        'files': ['DRAMP_general_amps.fasta', 'DRAMP_patent_amps.fasta'],\n", "        'description': '龙抗菌肽数据库',\n", "        'expected_format': 'fasta'\n", "    },\n", "    'CAMP': {\n", "        'url': 'http://www.camp3.bicnirrh.res.in/campdownload.php',\n", "        'description': 'CAMP抗菌肽数据库',\n", "        'expected_format': 'fasta'\n", "    }\n", "}\n", "\n", "print(\"数据库配置加载完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 数据下载工具类"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class AMPDataDownloader:\n", "    \"\"\"抗菌肽数据下载器\"\"\"\n", "    \n", "    def __init__(self, output_dir, max_retries=3, timeout=30):\n", "        self.output_dir = output_dir\n", "        self.max_retries = max_retries\n", "        self.timeout = timeout\n", "        self.download_log = []\n", "        \n", "    def calculate_file_hash(self, filepath):\n", "        \"\"\"计算文件的SHA256哈希值\"\"\"\n", "        hash_sha256 = hashlib.sha256()\n", "        with open(filepath, \"rb\") as f:\n", "            for chunk in iter(lambda: f.read(4096), b\"\"):\n", "                hash_sha256.update(chunk)\n", "        return hash_sha256.hexdigest()\n", "    \n", "    def download_file(self, url, filename, description=\"\"):\n", "        \"\"\"下载单个文件，包含重试机制\"\"\"\n", "        filepath = os.path.join(self.output_dir, filename)\n", "        \n", "        for attempt in range(self.max_retries):\n", "            try:\n", "                print(f\"正在下载 {description} (尝试 {attempt + 1}/{self.max_retries})...\")\n", "                \n", "                response = requests.get(url, timeout=self.timeout, stream=True)\n", "                response.raise_for_status()\n", "                \n", "                total_size = int(response.headers.get('content-length', 0))\n", "                \n", "                with open(filepath, 'wb') as f, tqdm(\n", "                    desc=filename,\n", "                    total=total_size,\n", "                    unit='B',\n", "                    unit_scale=True,\n", "                    unit_divisor=1024,\n", "                ) as pbar:\n", "                    for chunk in response.iter_content(chunk_size=8192):\n", "                        if chunk:\n", "                            f.write(chunk)\n", "                            pbar.update(len(chunk))\n", "                \n", "                # 验证文件完整性\n", "                file_hash = self.calculate_file_hash(filepath)\n", "                file_size = os.path.getsize(filepath)\n", "                \n", "                # 记录下载信息\n", "                download_info = {\n", "                    'filename': filename,\n", "                    'url': url,\n", "                    'description': description,\n", "                    'download_time': datetime.now().isoformat(),\n", "                    'file_size': file_size,\n", "                    'sha256_hash': file_hash,\n", "                    'status': 'success'\n", "                }\n", "                self.download_log.append(download_info)\n", "                \n", "                print(f\"✅ 成功下载 {filename} ({file_size:,} bytes)\")\n", "                return True\n", "                \n", "            except Exception as e:\n", "                print(f\"❌ 下载失败 (尝试 {attempt + 1}): {str(e)}\")\n", "                if attempt < self.max_retries - 1:\n", "                    time.sleep(2 ** attempt)  # 指数退避\n", "                else:\n", "                    # 记录失败信息\n", "                    download_info = {\n", "                        'filename': filename,\n", "                        'url': url,\n", "                        'description': description,\n", "                        'download_time': datetime.now().isoformat(),\n", "                        'error': str(e),\n", "                        'status': 'failed'\n", "                    }\n", "                    self.download_log.append(download_info)\n", "        \n", "        return False\n", "    \n", "    def save_download_log(self):\n", "        \"\"\"保存下载日志\"\"\"\n", "        log_file = os.path.join(self.output_dir, f'download_log_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.json')\n", "        with open(log_file, 'w', encoding='utf-8') as f:\n", "            json.dump(self.download_log, f, ensure_ascii=False, indent=2)\n", "        print(f\"下载日志已保存到: {log_file}\")\n", "\n", "print(\"✅ AMPDataDownloader类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 开始数据下载"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化下载器\n", "downloader = AMPDataDownloader(DATA_RAW_DIR)\n", "\n", "print(\"开始下载抗菌肽数据...\")\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 下载dbAMP数据\n", "db_config = DATABASE_CONFIG['dbAMP']\n", "filename = f\"dbAMP_pos_{datetime.now().strftime('%Y%m%d')}.fasta\"\n", "\n", "success = downloader.download_file(\n", "    url=db_config['url'],\n", "    filename=filename,\n", "    description=db_config['description']\n", ")\n", "\n", "if not success:\n", "    print(\"尝试备用URL...\")\n", "    success = downloader.download_file(\n", "        url=db_config['backup_url'],\n", "        filename=filename,\n", "        description=f\"{db_config['description']} (备用源)\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 数据质量初步检查"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_fasta_file(filepath):\n", "    \"\"\"分析FASTA文件的基本统计信息\"\"\"\n", "    if not os.path.exists(filepath):\n", "        print(f\"文件不存在: {filepath}\")\n", "        return None\n", "    \n", "    sequences = []\n", "    sequence_lengths = []\n", "    invalid_chars = set()\n", "    valid_aa = set('ACDEFGHIKLMNPQRSTVWY')\n", "    \n", "    try:\n", "        for record in SeqIO.parse(filepath, \"fasta\"):\n", "            seq_str = str(record.seq).upper()\n", "            sequences.append(seq_str)\n", "            sequence_lengths.append(len(seq_str))\n", "            \n", "            # 检查无效字符\n", "            for char in seq_str:\n", "                if char not in valid_aa:\n", "                    invalid_chars.add(char)\n", "        \n", "        stats = {\n", "            'total_sequences': len(sequences),\n", "            'min_length': min(sequence_lengths) if sequence_lengths else 0,\n", "            'max_length': max(sequence_lengths) if sequence_lengths else 0,\n", "            'mean_length': np.mean(sequence_lengths) if sequence_lengths else 0,\n", "            'median_length': np.median(sequence_lengths) if sequence_lengths else 0,\n", "            'invalid_characters': list(invalid_chars),\n", "            'file_size_mb': os.path.getsize(filepath) / (1024 * 1024)\n", "        }\n", "        \n", "        return stats\n", "        \n", "    except Exception as e:\n", "        print(f\"分析文件时出错: {e}\")\n", "        return None\n", "\n", "print(\"✅ 数据分析函数定义完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析已下载的文件\n", "print(\"正在分析下载的数据文件...\")\n", "print(\"=\" * 40)\n", "\n", "for log_entry in downloader.download_log:\n", "    if log_entry['status'] == 'success':\n", "        filepath = os.path.join(DATA_RAW_DIR, log_entry['filename'])\n", "        stats = analyze_fasta_file(filepath)\n", "        \n", "        if stats:\n", "            print(f\"\\n📊 {log_entry['filename']} 统计信息:\")\n", "            print(f\"  序列总数: {stats['total_sequences']:,}\")\n", "            print(f\"  长度范围: {stats['min_length']} - {stats['max_length']}\")\n", "            print(f\"  平均长度: {stats['mean_length']:.1f}\")\n", "            print(f\"  中位数长度: {stats['median_length']:.1f}\")\n", "            print(f\"  文件大小: {stats['file_size_mb']:.2f} MB\")\n", "            if stats['invalid_characters']:\n", "                print(f\"  ⚠️ 发现无效字符: {stats['invalid_characters']}\")\n", "            else:\n", "                print(f\"  ✅ 所有字符均为标准氨基酸\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 保存下载日志和总结"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存下载日志\n", "downloader.save_download_log()\n", "\n", "# 生成总结报告\n", "successful_downloads = [log for log in downloader.download_log if log['status'] == 'success']\n", "failed_downloads = [log for log in downloader.download_log if log['status'] == 'failed']\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"📋 数据下载总结报告\")\n", "print(\"=\" * 50)\n", "print(f\"成功下载: {len(successful_downloads)} 个文件\")\n", "print(f\"下载失败: {len(failed_downloads)} 个文件\")\n", "\n", "if successful_downloads:\n", "    total_size = sum(log['file_size'] for log in successful_downloads)\n", "    print(f\"总数据量: {total_size / (1024 * 1024):.2f} MB\")\n", "\n", "if failed_downloads:\n", "    print(\"\\n❌ 失败的下载:\")\n", "    for log in failed_downloads:\n", "        print(f\"  - {log['filename']}: {log.get('error', '未知错误')}\")\n", "\n", "print(f\"\\n📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"\\n✅ 第一阶段数据收集完成！\")\n", "print(\"\\n📝 下一步: 运行 02_数据清洗与预处理.ipynb\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 4}