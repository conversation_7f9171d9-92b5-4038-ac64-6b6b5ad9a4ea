# 初始化下载器
downloader = AMPDataDownloader(DATA_RAW_DIR)

print("开始下载抗菌肽数据...")
print("=" * 50)
print("提示：如下载文件较大，下载过程中请耐心等待。")

# 如果AMPDataDownloader未实现进度条，可在download_file方法中传递回调函数用于显示进度
# 这里定义一个简单的进度显示函数
def show_progress(block_num, block_size, total_size):
    downloaded = block_num * block_size
    percent = min(100, downloaded * 100 // (total_size or 1))
    bar = '█' * (percent // 2) + '-' * (50 - percent // 2)
    print(f"\r下载进度: |{bar}| {percent}%", end='', flush=True)
    if downloaded >= total_size:
        print()  # 换行