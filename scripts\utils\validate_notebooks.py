# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-20
# 描述: 验证Jupyter Notebook文件的完整性和格式正确性

import json
import os
import sys
from pathlib import Path

def validate_notebook(notebook_path):
    """验证单个notebook文件"""
    try:
        with open(notebook_path, 'r', encoding='utf-8') as f:
            nb_data = json.load(f)
        
        # 检查必要的字段
        required_fields = ['cells', 'metadata', 'nbformat', 'nbformat_minor']
        missing_fields = [field for field in required_fields if field not in nb_data]
        
        if missing_fields:
            return False, f"缺少必要字段: {missing_fields}"
        
        # 检查cells是否为列表
        if not isinstance(nb_data['cells'], list):
            return False, "cells字段必须是列表"
        
        # 检查每个cell的基本结构
        for i, cell in enumerate(nb_data['cells']):
            if 'cell_type' not in cell:
                return False, f"第{i+1}个cell缺少cell_type字段"
            
            if cell['cell_type'] not in ['code', 'markdown', 'raw']:
                return False, f"第{i+1}个cell的cell_type无效: {cell['cell_type']}"
        
        return True, "格式正确"
        
    except json.JSONDecodeError as e:
        return False, f"JSON格式错误: {e}"
    except FileNotFoundError:
        return False, "文件不存在"
    except Exception as e:
        return False, f"未知错误: {e}"

def validate_all_notebooks(project_root="."):
    """验证项目中的所有notebook文件"""
    project_path = Path(project_root)
    notebook_files = list(project_path.rglob("*.ipynb"))
    
    if not notebook_files:
        print("❌ 未找到任何notebook文件")
        return False
    
    print(f"🔍 找到 {len(notebook_files)} 个notebook文件")
    print("=" * 60)
    
    all_valid = True
    
    for nb_file in sorted(notebook_files):
        relative_path = nb_file.relative_to(project_path)
        is_valid, message = validate_notebook(nb_file)
        
        if is_valid:
            print(f"✅ {relative_path} - {message}")
        else:
            print(f"❌ {relative_path} - {message}")
            all_valid = False
    
    print("=" * 60)
    
    if all_valid:
        print("🎉 所有notebook文件都通过验证！")
    else:
        print("⚠️ 发现有问题的notebook文件，请检查并修复")
    
    return all_valid

def fix_common_issues(notebook_path):
    """修复常见的notebook格式问题"""
    try:
        with open(notebook_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复常见的JSON格式问题
        fixes_applied = []
        
        # 移除多余的反斜杠和换行符
        if '\\n' in content and content.count('\\n') > content.count('\\\\n'):
            content = content.replace('\\n', '\n')
            fixes_applied.append("移除多余的反斜杠换行符")
        
        # 确保文件以换行符结尾
        if not content.endswith('\n'):
            content += '\n'
            fixes_applied.append("添加文件结尾换行符")
        
        # 验证修复后的JSON
        try:
            json.loads(content)
        except json.JSONDecodeError:
            return False, "修复后仍然不是有效的JSON格式"
        
        # 写回文件
        with open(notebook_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        if fixes_applied:
            return True, f"已修复: {', '.join(fixes_applied)}"
        else:
            return True, "无需修复"
            
    except Exception as e:
        return False, f"修复失败: {e}"

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 验证指定的notebook文件
        notebook_path = sys.argv[1]
        print(f"🔍 验证notebook: {notebook_path}")
        
        is_valid, message = validate_notebook(notebook_path)
        
        if is_valid:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            
            # 尝试修复
            print("🔧 尝试自动修复...")
            fixed, fix_message = fix_common_issues(notebook_path)
            
            if fixed:
                print(f"✅ 修复成功: {fix_message}")
                # 重新验证
                is_valid, message = validate_notebook(notebook_path)
                if is_valid:
                    print("✅ 修复后验证通过")
                else:
                    print(f"❌ 修复后仍有问题: {message}")
            else:
                print(f"❌ 修复失败: {fix_message}")
    else:
        # 验证所有notebook文件
        print("🔍 验证项目中的所有notebook文件...")
        validate_all_notebooks()

if __name__ == "__main__":
    main()
