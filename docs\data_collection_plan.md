# 抗革兰氏阴性菌高活性抗菌肽数据收集详细计划

**项目目标**: 系统性收集1,000-2,000个高质量抗革兰氏阴性菌肽序列，构建专门的机器学习分类模型数据集

**计划制定时间**: 2025-07-21  
**预计完成时间**: 2025-08-15 (25个工作日)  
**项目负责人**: ZK

---

## 📋 项目概览

### 核心目标
- **数量目标**: 1,000-2,000个高质量序列
- **质量标准**: MIC ≤ 32 μg/mL，针对革兰氏阴性菌
- **应用目标**: 构建专门的机器学习分类模型
- **数据完整性**: 每个序列包含完整的活性和来源信息

### 成功指标
- [ ] 收集序列数量 ≥ 1,000个
- [ ] 高活性序列比例 ≥ 70% (MIC ≤ 16 μg/mL)
- [ ] 数据完整性 ≥ 95% (包含MIC值和菌株信息)
- [ ] 去重后保留率 ≥ 80%
- [ ] 文献可追溯性 = 100%

---

## 🎯 阶段1: 项目准备与环境搭建 (第1-2天)

### 1.1 创建数据收集工作环境

**目录结构**:
```
data_collection/
├── raw_data/           # 原始数据
│   ├── dbaasp/        # DBAASP数据
│   ├── campr4/        # CAMPR4数据
│   ├── apd/           # APD数据
│   └── literature/    # 文献数据
├── processed_data/     # 处理后数据
├── templates/          # 数据模板
├── scripts/           # 处理脚本
├── references/        # 文献管理
└── reports/           # 进度报告
```

**工具准备**:
- Excel/Google Sheets (数据记录)
- Zotero/Mendeley (文献管理)
- Python环境 (数据处理)
- 序列分析工具 (BLAST, Clustal)

### 1.2 制定数据质量评估标准

#### 核心质量标准

| 标准类别 | 要求 | 权重 | 验证方法 |
|----------|------|------|----------|
| **活性标准** | MIC ≤ 32 μg/mL | ⭐⭐⭐⭐⭐ | 文献验证 |
| **高活性标准** | MIC ≤ 16 μg/mL | ⭐⭐⭐⭐⭐ | 优先收集 |
| **菌株特异性** | 明确的革兰氏阴性菌株 | ⭐⭐⭐⭐⭐ | 数据库确认 |
| **序列长度** | 10-50 氨基酸 | ⭐⭐⭐⭐ | 自动筛选 |
| **序列质量** | 标准氨基酸，无模糊字符 | ⭐⭐⭐⭐ | 格式验证 |
| **实验条件** | 明确的实验方法 | ⭐⭐⭐ | 文献检查 |
| **来源可靠性** | 同行评议期刊 | ⭐⭐⭐⭐⭐ | 期刊验证 |

#### 目标菌株优先级

**一级目标菌株** (必须收集):
- *Escherichia coli* (大肠杆菌)
- *Pseudomonas aeruginosa* (铜绿假单胞菌)
- *Acinetobacter baumannii* (鲍曼不动杆菌)

**二级目标菌株** (重要补充):
- *Klebsiella pneumoniae* (肺炎克雷伯菌)
- *Salmonella* spp. (沙门氏菌)
- *Enterobacter* spp. (肠杆菌)

**三级目标菌株** (可选):
- *Proteus* spp. (变形杆菌)
- *Serratia* spp. (沙雷氏菌)
- *Citrobacter* spp. (柠檬酸杆菌)

### 1.3 设计标准化数据记录模板

#### 主数据表字段设计

| 字段名 | 数据类型 | 必填 | 说明 | 示例 |
|--------|----------|------|------|------|
| `Peptide_ID` | 文本 | ✅ | 唯一标识符 | GN_AMP_001 |
| `Sequence` | 文本 | ✅ | 氨基酸序列 | KWKLFKKIEKVGQNIR |
| `Length` | 数值 | ✅ | 序列长度 | 16 |
| `Target_Organism` | 文本 | ✅ | 目标菌株 | Escherichia coli |
| `MIC_Value` | 数值 | ✅ | MIC值 | 8.0 |
| `MIC_Unit` | 文本 | ✅ | MIC单位 | μg/mL |
| `Activity_Level` | 文本 | ✅ | 活性等级 | High (≤16) |
| `Source_Database` | 文本 | ✅ | 来源数据库 | DBAASP |
| `Original_ID` | 文本 | ✅ | 原始ID | DBAASP_12345 |
| `Reference_PMID` | 文本 | ✅ | 文献PMID | 12345678 |
| `Reference_DOI` | 文本 | ⚠️ | 文献DOI | 10.1038/xxx |
| `Experimental_Method` | 文本 | ⚠️ | 实验方法 | Broth microdilution |
| `Culture_Conditions` | 文本 | ⚠️ | 培养条件 | MHB, 37°C |
| `Collection_Date` | 日期 | ✅ | 收集日期 | 2025-07-21 |
| `Collector` | 文本 | ✅ | 收集人员 | ZK |
| `Quality_Score` | 数值 | ✅ | 质量评分 | 9.2 |
| `Notes` | 文本 | ⚠️ | 备注信息 | 高活性，优先使用 |

#### 质量评分算法

```python
def calculate_quality_score(record):
    score = 0
    
    # MIC值评分 (40%)
    if record['MIC_Value'] <= 4:
        score += 4.0
    elif record['MIC_Value'] <= 8:
        score += 3.5
    elif record['MIC_Value'] <= 16:
        score += 3.0
    elif record['MIC_Value'] <= 32:
        score += 2.0
    else:
        score += 1.0
    
    # 菌株特异性 (20%)
    if record['Target_Organism'] in ['E. coli', 'P. aeruginosa', 'A. baumannii']:
        score += 2.0
    elif 'gram-negative' in record['Target_Organism'].lower():
        score += 1.5
    else:
        score += 1.0
    
    # 序列长度 (15%)
    length = record['Length']
    if 15 <= length <= 35:
        score += 1.5
    elif 10 <= length <= 50:
        score += 1.2
    else:
        score += 0.8
    
    # 文献质量 (15%)
    if record['Reference_PMID']:
        score += 1.5
    else:
        score += 0.5
    
    # 数据完整性 (10%)
    completeness = sum([
        bool(record.get('Experimental_Method')),
        bool(record.get('Culture_Conditions')),
        bool(record.get('Reference_DOI'))
    ]) / 3
    score += completeness * 1.0
    
    return round(score, 1)
```

### 1.4 建立文献管理系统

#### 文献分类体系

**按期刊影响因子分类**:
- **Tier 1** (IF > 10): Nature, Science, Cell系列
- **Tier 2** (IF 5-10): 专业顶级期刊
- **Tier 3** (IF 2-5): 主流专业期刊
- **Tier 4** (IF < 2): 其他期刊

**按研究类型分类**:
- **Discovery**: 新肽发现研究
- **SAR**: 构效关系研究  
- **Mechanism**: 作用机制研究
- **Clinical**: 临床相关研究

#### 文献追踪表格

| 字段 | 说明 | 示例 |
|------|------|------|
| `Paper_ID` | 文献唯一ID | LIT_001 |
| `PMID` | PubMed ID | 12345678 |
| `DOI` | 数字对象标识符 | 10.1038/xxx |
| `Title` | 文献标题 | Novel AMPs against... |
| `Journal` | 期刊名称 | Nature Biotechnology |
| `Impact_Factor` | 影响因子 | 15.2 |
| `Year` | 发表年份 | 2024 |
| `Peptides_Count` | 包含肽数量 | 15 |
| `Quality_Tier` | 质量等级 | Tier 1 |
| `Status` | 处理状态 | Extracted |

---

## 🔍 阶段2: DBAASP数据库精准收集 (第3-8天)

### 2.1 DBAASP高级搜索设置

#### 访问地址
```
https://dbaasp.org/search
```

#### 精准搜索参数配置

**基础筛选条件**:
```
Target Organism: 
- Escherichia coli
- Pseudomonas aeruginosa  
- Acinetobacter baumannii
- Klebsiella pneumoniae
- Salmonella enterica

Activity Type: Antibacterial

Peptide Length: 10-50 amino acids

Activity Threshold: MIC ≤ 32 μg/mL
```

**高级筛选条件**:
```
Source: Natural peptides (优先)
Experimental Method: Broth microdilution (优先)
Data Quality: Peer-reviewed publications only
```

#### 搜索策略

**第一轮搜索** (高活性肽):
- MIC ≤ 8 μg/mL
- 目标: 200-300个序列
- 预计时间: 1天

**第二轮搜索** (中等活性肽):
- MIC 8-16 μg/mL  
- 目标: 300-400个序列
- 预计时间: 1天

**第三轮搜索** (可接受活性肽):
- MIC 16-32 μg/mL
- 目标: 200-300个序列
- 预计时间: 1天

### 2.2 按菌株分类收集

#### 每日收集计划

**第3天: E. coli专项收集**
- 目标: 200-250个序列
- 重点: MIC ≤ 16 μg/mL的高活性肽
- 验证: 每个序列的实验条件

**第4天: P. aeruginosa专项收集**  
- 目标: 150-200个序列
- 重点: 抗铜绿假单胞菌的特异性肽
- 验证: 耐药菌株的活性数据

**第5天: A. baumannii专项收集**
- 目标: 100-150个序列  
- 重点: 抗多重耐药鲍曼不动杆菌
- 验证: 临床分离株的活性

**第6天: 其他革兰氏阴性菌**
- 目标: 100-150个序列
- 包括: Klebsiella, Salmonella, Enterobacter
- 验证: 交叉活性数据

### 2.3 MIC值验证与数据清洗

#### 数据验证流程

**步骤1: 自动验证**
```python
def validate_mic_data(record):
    checks = {
        'mic_numeric': isinstance(record['MIC_Value'], (int, float)),
        'mic_positive': record['MIC_Value'] > 0,
        'mic_reasonable': record['MIC_Value'] <= 1000,
        'unit_standard': record['MIC_Unit'] in ['μg/mL', 'μM', 'mg/L']
    }
    return all(checks.values()), checks
```

**步骤2: 手动验证**
- 检查异常高/低的MIC值
- 验证实验方法的一致性
- 确认菌株名称的准确性

**步骤3: 文献交叉验证**
- 对关键数据点进行文献核实
- 解决数据冲突和不一致

#### 数据清洗标准

**删除条件**:
- MIC值缺失或无效
- 序列包含非标准氨基酸 >10%
- 无法验证的文献来源
- 重复序列 (相似度 >95%)

**标记条件**:
- MIC值异常 (需要验证)
- 实验条件不明确
- 文献质量较低

### 2.4 初步质量控制

#### 质量控制检查点

**数据完整性检查**:
- [ ] 所有必填字段完整
- [ ] MIC值在合理范围内
- [ ] 序列格式正确
- [ ] 文献信息可追溯

**科学合理性检查**:
- [ ] 活性数据与序列特征一致
- [ ] 实验条件符合标准
- [ ] 菌株分类准确

**重复性检查**:
- [ ] 序列去重 (95%相似度阈值)
- [ ] 文献去重
- [ ] 数据源交叉验证

#### 每日质量报告模板

```
DBAASP数据收集日报 - 第X天
================================

收集统计:
- 新增序列: XXX个
- 累计序列: XXX个
- 高活性肽(≤16 μg/mL): XXX个
- 平均质量评分: X.X

质量指标:
- 数据完整性: XX%
- 文献可追溯性: XX%
- 重复序列: XX个 (已标记)

问题记录:
- 需要验证的异常数据: XX个
- 文献无法获取: XX个
- 其他问题: XXX

明日计划:
- 目标菌株: XXX
- 预期收集量: XXX个
- 重点关注: XXX
```

---

## 📚 阶段3: CAMPR4和APD数据库收集 (第9-12天)

### 3.1 CAMPR4数据库收集策略

#### 访问地址
```
https://camp.bicnirrh.res.in/
```

#### 搜索配置
**高级搜索参数**:
```
Target Organism: Gram-negative bacteria
Activity: Antibacterial
Source: Natural (优先)
Length: 10-50 amino acids
```

**收集重点**:
- 补充DBAASP中缺失的序列
- 重点关注合成肽的高活性变体
- 验证天然肽的活性数据

### 3.2 APD数据库收集策略

#### 手动收集流程
1. **访问APD网站**: https://aps.unmc.edu/
2. **使用高级搜索**:
   - Target: Gram-negative bacteria
   - Activity: Antibacterial
   - Length: 10-50 AA
3. **逐个记录高质量序列**
4. **交叉验证活性数据**

---

*[由于字符限制，完整计划将在下一个文件中继续...]*
