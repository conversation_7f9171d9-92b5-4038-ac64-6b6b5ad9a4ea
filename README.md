# 抗菌肽分类项目：基于ESM-2的深度学习方案

## 项目概述

本项目旨在构建基于ESM-2预训练模型的高精度抗菌肽(AMP)分类器，用于从蛋白质序列中识别具有抗菌活性的肽段。

## 项目结构

```
抗菌肽文章撰写/
├── README.md                    # 项目总览说明
├── 计划.md                      # 详细项目计划
├── data/                        # 数据目录
│   ├── raw/                     # 原始下载数据（只读）
│   ├── processed/               # 预处理、清洗后的中间数据
│   └── analysis_ready/          # 可直接用于模型或分析的最终数据
├── scripts/                     # 所有 Python 脚本
│   ├── data_download/           # 数据下载与同步脚本
│   ├── data_prep/               # 数据清洗与预处理脚本
│   ├── data_analysis/           # 统计分析、可视化或机器学习脚本
│   └── utils/                   # 通用工具函数脚本
├── papers/                      # 学术论文、图表及其素材
│   ├── drafts/                  # LaTeX/Markdown 初稿
│   ├── figures/                 # 所有图表（SVG/PNG/PDF 等）
│   └── supplementary/           # 补充材料
└── memory-bank/                 # 记忆库（项目管理）
    ├── archive/                 # 历史归档
    ├── project_brief.md         # 项目简介
    ├── system_patterns.md       # 系统模式
    ├── tech_stack.md           # 技术栈说明
    ├── active_context.md       # 活跃上下文
    └── progress_log.md         # 进度日志
```

## 技术栈

- **深度学习框架**: PyTorch 2.0+
- **预训练模型**: ESM-2 (Facebook AI Research)
- **数据处理**: BioPython, Pandas, NumPy
- **机器学习**: Scikit-learn
- **可视化**: Matplotlib, Seaborn
- **序列分析**: MMseqs2, CD-HIT
- **开发环境**: Jupyter Notebook
- **云计算**: 支持GPU的云服务器

## 数据来源

- **dbAMP**: 抗菌肽数据库
- **APD3**: 抗菌肽数据库第三版
- **CAMP**: 抗菌肽收集数据库
- **DRAMP**: 龙抗菌肽数据库
- **DBAASP**: 抗菌和抗病毒肽数据库

## 项目阶段

1. **数据收集与初步处理** (第1-3周)
2. **数据清洗与标准化** (第2-3周)
3. **ESM-2特征提取与数据集构建** (第3-4周)
4. **模型训练** (第4-5周)
5. **评估与验证** (第5-6周)
6. **模型解释与生物学验证** (第6-8周)

## 快速开始

### 环境配置
```bash
# 创建conda环境
conda create -n amp_esm2 python=3.9
conda activate amp_esm2

# 安装依赖
pip install -r requirements.txt
```

### 数据下载
```bash
cd scripts/data_download
python download_amp_databases.py
```

### 模型训练
```bash
cd scripts/data_analysis
python train_esm2_amp_classifier.py
```

## 作者信息

- **作者**: ZK
- **邮箱**: <EMAIL>
- **日期**: 2025-07-20

## 许可证

本项目仅用于学术研究目的。
