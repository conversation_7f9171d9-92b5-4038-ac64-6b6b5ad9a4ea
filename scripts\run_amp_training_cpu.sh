#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 在高算平台CPU节点上运行ESM-2抗菌肽训练的SLURM作业脚本

#SBATCH --job-name=amp_esm2_cpu         # 作业名称
#SBATCH --partition=cpu                 # CPU分区
#SBATCH --cpus-per-task=16              # CPU核心数（根据需要调整）
#SBATCH --mem=64G                       # 内存（ESM-2需要较大内存）
#SBATCH --time=24:00:00                 # 最大运行时间（24小时）
#SBATCH --output=logs/amp_training_%j.out    # 标准输出文件
#SBATCH --error=logs/amp_training_%j.err     # 错误输出文件
#SBATCH --mail-type=BEGIN,END,FAIL      # 邮件通知类型
#SBATCH --mail-user=<EMAIL>    # 邮件地址

# 创建必要目录
mkdir -p logs
mkdir -p results/checkpoints
mkdir -p results/figures
mkdir -p models

# 打印作业信息
echo "=========================================="
echo "🧬 ESM-2抗菌肽分类训练任务"
echo "=========================================="
echo "作业ID: $SLURM_JOB_ID"
echo "节点名称: $SLURMD_NODENAME"
echo "开始时间: $(date)"
echo "CPU核心数: $SLURM_CPUS_PER_TASK"
echo "分配内存: 64GB"
echo "预计运行时间: 24小时"
echo "=========================================="

# 加载必要的模块（根据您的集群环境调整）
# 常见的模块加载命令，请根据您的高算平台调整
# module load anaconda3/2023.03
# module load python/3.9
# module load gcc/9.3.0

# 激活conda环境
echo "🔧 激活conda环境..."
source ~/miniconda3/etc/profile.d/conda.sh || source ~/anaconda3/etc/profile.d/conda.sh
conda activate amp_esm2

# 验证环境
echo "🔍 验证环境配置..."
echo "Python版本: $(python --version)"
echo "工作目录: $(pwd)"
echo "Conda环境: $CONDA_DEFAULT_ENV"

# 设置环境变量优化CPU性能
export OMP_NUM_THREADS=$SLURM_CPUS_PER_TASK
export MKL_NUM_THREADS=$SLURM_CPUS_PER_TASK
export NUMEXPR_NUM_THREADS=$SLURM_CPUS_PER_TASK
export OPENBLAS_NUM_THREADS=$SLURM_CPUS_PER_TASK

echo "🔧 CPU优化设置:"
echo "  - OMP_NUM_THREADS: $OMP_NUM_THREADS"
echo "  - MKL_NUM_THREADS: $MKL_NUM_THREADS"

# 验证关键依赖
echo "📦 验证关键依赖..."
python -c "
import sys
print(f'Python: {sys.version}')

try:
    import torch
    print(f'✅ PyTorch: {torch.__version__}')
    print(f'CPU线程数: {torch.get_num_threads()}')
    
    import pandas as pd
    print(f'✅ Pandas: {pd.__version__}')
    
    import numpy as np
    print(f'✅ NumPy: {np.__version__}')
    
    from Bio import SeqIO
    print('✅ BioPython: 导入成功')
    
    import esm
    print('✅ ESM: 导入成功')
    
except ImportError as e:
    print(f'❌ 依赖检查失败: {e}')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 环境验证失败，退出任务"
    exit 1
fi

# 检查数据文件
echo "📁 检查数据文件..."
if [ ! -f "ESM2_AMP_Training_Complete.ipynb" ]; then
    echo "❌ 未找到主要训练文件: ESM2_AMP_Training_Complete.ipynb"
    exit 1
fi

# 创建Python执行脚本（从notebook转换）
echo "📝 准备训练脚本..."
cat > run_training.py << 'EOF'
#!/usr/bin/env python3
"""
ESM-2抗菌肽分类训练脚本
从Jupyter Notebook转换而来，适合在高算平台上运行
"""

import os
import sys
import time
import pickle
import warnings
from pathlib import Path

# 设置工作目录
os.chdir(os.path.dirname(os.path.abspath(__file__)))

# 忽略警告
warnings.filterwarnings('ignore')

# 导入必要的库
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.cuda.amp import autocast, GradScaler

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
from Bio import SeqIO
import esm

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns

from tqdm import tqdm
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """主训练函数"""
    logger.info("🚀 开始ESM-2抗菌肽分类训练")
    
    # 设置设备
    device = torch.device('cpu')
    logger.info(f"使用设备: {device}")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        # 这里应该包含您的训练代码
        # 由于notebook内容较长，建议您将核心训练逻辑提取到这里
        
        logger.info("✅ 训练准备完成")
        
        # 示例：加载ESM-2模型
        logger.info("📥 加载ESM-2模型...")
        model, alphabet = esm.pretrained.esm2_t6_8M_UR50D()
        model = model.to(device)
        logger.info("✅ ESM-2模型加载完成")
        
        # 这里添加您的具体训练逻辑
        # 建议从您的notebook中复制核心代码
        
        logger.info("🎉 训练完成！")
        
    except Exception as e:
        logger.error(f"❌ 训练过程中出现错误: {e}")
        raise e

if __name__ == "__main__":
    main()
EOF

# 运行训练
echo "🚀 开始训练..."
echo "开始时间: $(date)"

# 使用Python运行训练脚本
python run_training.py

# 检查训练结果
TRAIN_EXIT_CODE=$?

echo "=========================================="
echo "📊 训练任务完成"
echo "=========================================="
echo "结束时间: $(date)"
echo "退出代码: $TRAIN_EXIT_CODE"

# 收集结果文件
echo "📁 收集结果文件..."
if [ -d "results" ]; then
    echo "结果目录内容:"
    ls -la results/
fi

if [ -d "models" ]; then
    echo "模型目录内容:"
    ls -la models/
fi

if [ -d "logs" ]; then
    echo "日志文件:"
    ls -la logs/
fi

# 生成任务报告
echo "📋 生成任务报告..."
cat > results/job_report_${SLURM_JOB_ID}.txt << EOF
ESM-2抗菌肽分类训练任务报告
========================================
作业ID: $SLURM_JOB_ID
节点: $SLURMD_NODENAME
开始时间: $(date)
CPU核心数: $SLURM_CPUS_PER_TASK
内存: 64GB
退出代码: $TRAIN_EXIT_CODE

环境信息:
- Python: $(python --version)
- PyTorch: $(python -c 'import torch; print(torch.__version__)')
- 工作目录: $(pwd)

文件统计:
- 结果文件数: $(find results/ -type f 2>/dev/null | wc -l)
- 模型文件数: $(find models/ -type f 2>/dev/null | wc -l)
- 日志文件数: $(find logs/ -type f 2>/dev/null | wc -l)

任务状态: $([ $TRAIN_EXIT_CODE -eq 0 ] && echo "成功" || echo "失败")
EOF

echo "✅ 任务报告已生成: results/job_report_${SLURM_JOB_ID}.txt"

# 清理临时文件
echo "🧹 清理临时文件..."
rm -f run_training.py

echo "=========================================="
if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    echo "🎉 训练任务成功完成！"
else
    echo "❌ 训练任务失败，请检查日志文件"
fi
echo "=========================================="

exit $TRAIN_EXIT_CODE
