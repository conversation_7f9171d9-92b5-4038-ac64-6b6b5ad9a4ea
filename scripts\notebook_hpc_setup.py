#!/usr/bin/env python3
"""
高算平台Notebook环境配置脚本
在ESM2_AMP_Training_Complete.ipynb开头运行此代码
作者: ZK
邮箱: <EMAIL>
日期: 2025-01-27
"""

import os
import sys
import torch
import gc
import psutil
import warnings
from pathlib import Path

# 忽略警告
warnings.filterwarnings('ignore')

print("🚀 初始化高算平台Jupyter环境...")
print("=" * 60)

# 1. 环境信息
print("📊 环境信息:")
print(f"  - Python版本: {sys.version}")
print(f"  - 工作目录: {os.getcwd()}")
print(f"  - CPU核心数: {os.cpu_count()}")

# 获取SLURM环境变量
slurm_cpus = os.environ.get('SLURM_CPUS_PER_TASK', str(os.cpu_count()))
slurm_mem = os.environ.get('SLURM_MEM_PER_NODE', 'Unknown')
slurm_job_id = os.environ.get('SLURM_JOB_ID', 'Interactive')

print(f"  - SLURM作业ID: {slurm_job_id}")
print(f"  - 分配CPU数: {slurm_cpus}")
print(f"  - 分配内存: {slurm_mem}")

# 2. PyTorch配置
print("\n🔧 PyTorch配置:")
print(f"  - PyTorch版本: {torch.__version__}")
print(f"  - CUDA可用: {torch.cuda.is_available()}")

# 设置CPU线程数
cpu_threads = int(slurm_cpus)
torch.set_num_threads(cpu_threads)
print(f"  - 设置CPU线程数: {cpu_threads}")

# 设置环境变量
os.environ['OMP_NUM_THREADS'] = str(cpu_threads)
os.environ['MKL_NUM_THREADS'] = str(cpu_threads)
os.environ['NUMEXPR_NUM_THREADS'] = str(cpu_threads)
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

if torch.cuda.is_available():
    print(f"  - GPU数量: {torch.cuda.device_count()}")
    for i in range(torch.cuda.device_count()):
        print(f"  - GPU {i}: {torch.cuda.get_device_name(i)}")
else:
    print("  - 使用CPU模式")

# 3. 内存监控函数
def monitor_memory():
    """监控内存使用情况"""
    memory = psutil.virtual_memory()
    print(f"💾 内存状态:")
    print(f"  - 总内存: {memory.total / (1024**3):.1f} GB")
    print(f"  - 已使用: {memory.used / (1024**3):.1f} GB ({memory.percent:.1f}%)")
    print(f"  - 可用内存: {memory.available / (1024**3):.1f} GB")
    
    if memory.percent > 85:
        print("⚠️  内存使用率较高，建议清理内存")
    
    return memory.available / (1024**3)  # 返回可用内存GB数

# 4. 内存清理函数
def clear_memory():
    """清理内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    print("🧹 内存清理完成")

# 5. 最优批处理大小计算
def get_optimal_batch_size():
    """根据可用内存计算最优批处理大小"""
    available_memory = monitor_memory()
    
    if available_memory > 50:
        batch_size = 32
    elif available_memory > 30:
        batch_size = 16
    elif available_memory > 20:
        batch_size = 8
    elif available_memory > 10:
        batch_size = 4
    else:
        batch_size = 2
    
    print(f"🎯 推荐批处理大小: {batch_size}")
    return batch_size

# 6. 创建必要目录
def setup_directories():
    """创建项目目录结构"""
    directories = [
        'data/raw',
        'data/processed', 
        'data/analysis_ready',
        'models',
        'results/figures',
        'results/tables',
        'results/checkpoints',
        'logs',
        'temp'
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("📁 目录结构创建完成")

# 7. ESM模型选择函数
def select_esm_model():
    """根据可用资源选择合适的ESM模型"""
    available_memory = psutil.virtual_memory().available / (1024**3)
    
    if available_memory > 40:
        model_name = 'esm2_t33_650M_UR50D'
        print("🧠 推荐使用: ESM-2 650M模型（大模型，高精度）")
    elif available_memory > 20:
        model_name = 'esm2_t12_35M_UR50D'
        print("🧠 推荐使用: ESM-2 35M模型（中等模型）")
    else:
        model_name = 'esm2_t6_8M_UR50D'
        print("🧠 推荐使用: ESM-2 8M模型（小模型，适合CPU）")
    
    return model_name

# 8. 检查点管理
class CheckpointManager:
    """检查点管理器"""
    
    def __init__(self, checkpoint_dir="results/checkpoints"):
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
    
    def save_checkpoint(self, model, optimizer, epoch, loss, metrics=None, filename=None):
        """保存检查点"""
        if filename is None:
            filename = f"checkpoint_epoch_{epoch}.pth"
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'loss': loss,
            'metrics': metrics or {},
            'timestamp': torch.tensor(torch.get_rng_state()).sum().item()
        }
        
        filepath = self.checkpoint_dir / filename
        torch.save(checkpoint, filepath)
        print(f"✅ 检查点已保存: {filename}")
        return filepath
    
    def load_checkpoint(self, model, optimizer, filename):
        """加载检查点"""
        filepath = self.checkpoint_dir / filename
        if not filepath.exists():
            raise FileNotFoundError(f"检查点文件不存在: {filename}")
        
        checkpoint = torch.load(filepath, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        print(f"✅ 检查点已加载: {filename}")
        return checkpoint['epoch'], checkpoint['loss'], checkpoint.get('metrics', {})
    
    def list_checkpoints(self):
        """列出所有检查点"""
        checkpoints = list(self.checkpoint_dir.glob("checkpoint_epoch_*.pth"))
        checkpoints.sort(key=lambda x: x.stat().st_mtime)
        return checkpoints
    
    def get_latest_checkpoint(self):
        """获取最新检查点"""
        checkpoints = self.list_checkpoints()
        return checkpoints[-1] if checkpoints else None

# 9. 训练进度监控
def create_progress_callback():
    """创建训练进度回调函数"""
    def progress_callback(epoch, loss, metrics=None):
        print(f"📊 Epoch {epoch}: Loss = {loss:.4f}")
        if metrics:
            for key, value in metrics.items():
                print(f"    {key}: {value:.4f}")
        
        # 内存监控
        if epoch % 5 == 0:
            monitor_memory()
            clear_memory()
    
    return progress_callback

# 执行初始化
print("\n🔧 执行环境初始化...")
setup_directories()
initial_memory = monitor_memory()
optimal_batch_size = get_optimal_batch_size()
recommended_model = select_esm_model()

# 创建全局对象
checkpoint_manager = CheckpointManager()
progress_callback = create_progress_callback()

print("\n" + "=" * 60)
print("✅ 高算平台环境初始化完成!")
print("=" * 60)

print("\n📋 使用建议:")
print(f"1. 使用批处理大小: {optimal_batch_size}")
print(f"2. 推荐ESM模型: {recommended_model}")
print("3. 定期调用 clear_memory() 清理内存")
print("4. 使用 checkpoint_manager 保存/加载检查点")
print("5. 每5-10个epoch保存一次检查点")

print("\n🔍 可用函数:")
print("- monitor_memory(): 监控内存使用")
print("- clear_memory(): 清理内存")
print("- checkpoint_manager.save_checkpoint(): 保存检查点")
print("- checkpoint_manager.load_checkpoint(): 加载检查点")

print("\n🚀 现在可以开始运行您的ESM-2训练代码了!")

# 导出重要变量供notebook使用
__all__ = [
    'monitor_memory', 'clear_memory', 'get_optimal_batch_size',
    'checkpoint_manager', 'progress_callback', 'optimal_batch_size',
    'recommended_model'
]
