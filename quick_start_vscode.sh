#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: VSCode高算平台快速启动脚本

echo "🚀 ESM-2抗菌肽项目 - VSCode高算平台快速启动"
echo "平台: s20223050931@**************"
echo "=========================================="

# 检查必要文件
if [ ! -f "scripts/vscode_hpc_integration.sh" ]; then
    echo "❌ 缺少集成脚本，请先运行完整部署"
    echo "   运行: ./deploy_to_hpc.sh"
    exit 1
fi

# 给脚本添加执行权限
chmod +x scripts/vscode_hpc_integration.sh

echo "📋 VSCode使用步骤:"
echo "1. 在VSCode中安装扩展: Remote-SSH, Python, Jupyter"
echo "2. 配置SSH连接 (参考 configs/ssh_config_template)"
echo "3. 连接到高算平台: s20223050931@**************"
echo "4. 在VSCode终端中运行:"
echo "   ./scripts/vscode_hpc_integration.sh"
echo ""
echo "🔗 详细指南: docs/vscode_hpc_guide.md"
echo ""
echo "✨ 现在可以在VSCode中连接到高算平台了!"
