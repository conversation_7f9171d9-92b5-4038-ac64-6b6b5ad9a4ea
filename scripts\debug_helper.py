#!/usr/bin/env python3
"""
ESM-2训练脚本调试辅助工具
作者: ZK
邮箱: <EMAIL>
日期: 2025-01-27
"""

import os
import sys
import time
import psutil
import torch
import gc
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)

def check_system_resources():
    """检查系统资源"""
    print_header("系统资源检查")
    
    # CPU信息
    cpu_count = os.cpu_count()
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"💻 CPU信息:")
    print(f"  - 核心数: {cpu_count}")
    print(f"  - 使用率: {cpu_percent}%")
    print(f"  - SLURM分配: {os.environ.get('SLURM_CPUS_PER_TASK', 'N/A')}")
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"\n💾 内存信息:")
    print(f"  - 总内存: {memory.total / (1024**3):.1f} GB")
    print(f"  - 已使用: {memory.used / (1024**3):.1f} GB ({memory.percent:.1f}%)")
    print(f"  - 可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"  - SLURM分配: {os.environ.get('SLURM_MEM_PER_NODE', 'N/A')} MB")
    
    # 磁盘信息
    disk = psutil.disk_usage('.')
    print(f"\n💿 磁盘信息:")
    print(f"  - 总空间: {disk.total / (1024**3):.1f} GB")
    print(f"  - 已使用: {disk.used / (1024**3):.1f} GB ({disk.used/disk.total*100:.1f}%)")
    print(f"  - 可用空间: {disk.free / (1024**3):.1f} GB")
    
    return {
        'cpu_count': cpu_count,
        'memory_gb': memory.available / (1024**3),
        'disk_gb': disk.free / (1024**3)
    }

def check_python_environment():
    """检查Python环境"""
    print_header("Python环境检查")
    
    print(f"🐍 Python信息:")
    print(f"  - 版本: {sys.version}")
    print(f"  - 路径: {sys.executable}")
    print(f"  - 工作目录: {os.getcwd()}")
    print(f"  - Conda环境: {os.environ.get('CONDA_DEFAULT_ENV', 'N/A')}")
    
    # 检查关键包
    packages = {
        'torch': 'PyTorch',
        'pandas': 'Pandas',
        'numpy': 'NumPy',
        'Bio': 'BioPython',
        'sklearn': 'Scikit-learn',
        'matplotlib': 'Matplotlib',
        'seaborn': 'Seaborn',
        'tqdm': 'TQDM',
        'esm': 'ESM'
    }
    
    print(f"\n📦 依赖包检查:")
    failed_packages = []
    
    for package, name in packages.items():
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'Unknown')
            print(f"  ✅ {name}: {version}")
        except ImportError as e:
            print(f"  ❌ {name}: 导入失败 - {e}")
            failed_packages.append(name)
    
    return len(failed_packages) == 0

def check_pytorch_config():
    """检查PyTorch配置"""
    print_header("PyTorch配置检查")
    
    try:
        import torch
        
        print(f"🔥 PyTorch信息:")
        print(f"  - 版本: {torch.__version__}")
        print(f"  - CPU线程数: {torch.get_num_threads()}")
        print(f"  - CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"  - CUDA版本: {torch.version.cuda}")
            print(f"  - GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  - GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print(f"  - 使用CPU模式")
        
        # 测试张量操作
        print(f"\n🧪 张量操作测试:")
        start_time = time.time()
        x = torch.randn(1000, 1000)
        y = torch.randn(1000, 1000)
        z = torch.mm(x, y)
        end_time = time.time()
        print(f"  ✅ 矩阵乘法测试通过 ({end_time - start_time:.3f}s)")
        
        return True
        
    except Exception as e:
        print(f"❌ PyTorch检查失败: {e}")
        return False

def test_esm_model():
    """测试ESM模型"""
    print_header("ESM模型测试")
    
    try:
        import esm
        
        print("🧬 ESM模型测试:")
        
        # 测试小模型加载
        print("  📥 加载ESM-2 8M模型...")
        start_time = time.time()
        model, alphabet = esm.pretrained.esm2_t6_8M_UR50D()
        load_time = time.time() - start_time
        print(f"  ✅ 模型加载成功 ({load_time:.2f}s)")
        
        # 测试数据处理
        print("  📊 测试数据处理...")
        batch_converter = alphabet.get_batch_converter()
        data = [
            ("protein1", "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"),
            ("protein2", "KALTARQQEVFDLIRDHISQTGMPPTRAEIAQRLGFRSPNAAEEHLKALARKGVIEIVSGASRGIRLLQEE"),
        ]
        batch_labels, batch_strs, batch_tokens = batch_converter(data)
        print(f"  ✅ 数据处理成功，批次大小: {batch_tokens.shape}")
        
        # 测试模型推理
        print("  🔮 测试模型推理...")
        model.eval()
        start_time = time.time()
        with torch.no_grad():
            results = model(batch_tokens, repr_layers=[6], return_contacts=True)
        inference_time = time.time() - start_time
        print(f"  ✅ 模型推理成功 ({inference_time:.2f}s)")
        
        # 检查输出
        token_representations = results["representations"][6]
        print(f"  📈 输出形状: {token_representations.shape}")
        
        # 清理内存
        del model, alphabet, results, token_representations
        gc.collect()
        print("  🧹 内存清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ ESM模型测试失败: {e}")
        return False

def suggest_batch_size(memory_gb):
    """根据内存建议批处理大小"""
    if memory_gb > 50:
        return 32
    elif memory_gb > 30:
        return 16
    elif memory_gb > 20:
        return 8
    elif memory_gb > 10:
        return 4
    else:
        return 2

def suggest_esm_model(memory_gb):
    """根据内存建议ESM模型"""
    if memory_gb > 40:
        return 'esm2_t33_650M_UR50D', '650M参数，高精度'
    elif memory_gb > 20:
        return 'esm2_t12_35M_UR50D', '35M参数，中等精度'
    else:
        return 'esm2_t6_8M_UR50D', '8M参数，适合CPU'

def generate_debug_report():
    """生成调试报告"""
    print_header("生成调试报告")
    
    # 收集系统信息
    resources = check_system_resources()
    python_ok = check_python_environment()
    pytorch_ok = check_pytorch_config()
    esm_ok = test_esm_model()
    
    # 生成建议
    batch_size = suggest_batch_size(resources['memory_gb'])
    model_name, model_desc = suggest_esm_model(resources['memory_gb'])
    
    # 生成报告
    report = f"""
# ESM-2训练环境调试报告
生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
节点: {os.environ.get('SLURMD_NODENAME', 'Unknown')}
作业ID: {os.environ.get('SLURM_JOB_ID', 'Interactive')}

## 系统资源
- CPU核心: {resources['cpu_count']}
- 可用内存: {resources['memory_gb']:.1f} GB
- 可用磁盘: {resources['disk_gb']:.1f} GB

## 环境检查结果
- Python环境: {'✅ 正常' if python_ok else '❌ 异常'}
- PyTorch配置: {'✅ 正常' if pytorch_ok else '❌ 异常'}
- ESM模型: {'✅ 正常' if esm_ok else '❌ 异常'}

## 推荐配置
- 推荐批处理大小: {batch_size}
- 推荐ESM模型: {model_name} ({model_desc})
- CPU线程数: {os.environ.get('SLURM_CPUS_PER_TASK', resources['cpu_count'])}

## 状态总结
{'🎉 环境配置正常，可以开始训练' if all([python_ok, pytorch_ok, esm_ok]) else '⚠️  环境存在问题，需要修复后再训练'}
"""
    
    # 保存报告
    report_file = f"debug_report_{time.strftime('%Y%m%d_%H%M%S')}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 调试报告已保存: {report_file}")
    print(report)
    
    return all([python_ok, pytorch_ok, esm_ok])

def main():
    """主函数"""
    print("🚀 ESM-2训练脚本调试辅助工具")
    print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 生成完整的调试报告
    success = generate_debug_report()
    
    if success:
        print("\n🎉 所有检查通过！您可以开始训练了。")
        print("\n📋 下一步建议:")
        print("1. 运行 jupyter lab 启动Jupyter环境")
        print("2. 打开 ESM2_AMP_Training_Complete.ipynb")
        print("3. 先运行环境配置单元")
        print("4. 开始您的ESM-2训练")
    else:
        print("\n⚠️  发现问题，请先解决环境问题再开始训练。")
        print("\n🔧 可能的解决方案:")
        print("1. 重新运行环境配置脚本")
        print("2. 检查conda环境是否正确激活")
        print("3. 验证依赖包是否正确安装")
    
    return success

if __name__ == "__main__":
    main()
