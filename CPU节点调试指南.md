# 🖥️ CPU节点调试指南

## 🎯 目标
在高算平台 `s20223050931@**************` 上申请CPU节点，配置amp_esm2环境，调试ESM-2训练脚本。

## 🚀 快速开始

### 方法1：一键启动调试会话（推荐）
```bash
# 运行调试启动脚本
chmod +x start_debug_session.sh
./start_debug_session.sh
```

### 方法2：直接申请CPU节点
```bash
# 运行CPU节点申请脚本
chmod +x scripts/debug_on_cpu_node.sh
./scripts/debug_on_cpu_node.sh
```

## 📋 调试会话选项

### 选项1：🖥️ 申请CPU节点进行交互式调试
- **资源配置**：16核CPU，64GB内存，4小时
- **适用场景**：完整的训练脚本调试
- **优势**：独占资源，稳定的调试环境

### 选项2：🧪 在当前环境运行快速检查
- **功能**：快速验证环境配置
- **适用场景**：环境问题排查
- **优势**：无需等待资源分配

### 选项3：📊 生成环境诊断报告
- **功能**：生成详细的环境诊断报告
- **适用场景**：问题分析和记录
- **优势**：完整的环境状态记录

### 选项4：🚀 启动Jupyter Lab调试环境
- **功能**：在已分配的节点上启动Jupyter
- **适用场景**：交互式代码调试
- **优势**：可视化调试界面

## 🔧 调试流程详解

### 第一步：申请CPU节点
```bash
./scripts/debug_on_cpu_node.sh
```

脚本会自动：
1. 检查当前是否在SLURM环境中
2. 申请交互式CPU节点（16核，64GB，4小时）
3. 等待节点分配
4. 在分配的节点上继续执行

### 第二步：环境配置验证
节点分配成功后，脚本会自动：
1. 激活amp_esm2 conda环境
2. 设置环境变量（CPU线程数等）
3. 验证Python环境和依赖包
4. 测试ESM模型加载

### 第三步：创建调试环境
```bash
# 自动创建调试目录
debug_session/20250127_143022/

# 复制必要文件
- ESM2_AMP_Training_Complete.ipynb
- hpc_notebook_setup.ipynb  
- scripts/
- configs/
```

### 第四步：交互式调试
进入调试会话后，您可以：

#### 基础环境测试
```bash
# 运行环境配置脚本
python scripts/notebook_hpc_setup.py

# 验证ESM兼容性
python scripts/verify_esm_compatibility.py

# 生成调试报告
python scripts/debug_helper.py
```

#### 启动Jupyter调试
```bash
# 启动Jupyter Lab
jupyter lab --ip=0.0.0.0 --port=8888 --no-browser

# 在另一个终端建立SSH隧道（本地执行）
ssh -L 8888:节点IP:8888 s20223050931@**************
```

#### 快速环境测试
```bash
# 一行命令测试环境
python -c "exec(open('scripts/notebook_hpc_setup.py').read())"
```

## 📊 调试辅助工具

### debug_helper.py 功能
1. **系统资源检查**：CPU、内存、磁盘使用情况
2. **Python环境检查**：版本、路径、依赖包
3. **PyTorch配置检查**：版本、线程数、CUDA状态
4. **ESM模型测试**：模型加载、数据处理、推理测试
5. **配置建议**：批处理大小、模型选择建议
6. **调试报告生成**：完整的环境状态报告

### 使用示例
```bash
# 运行完整检查
python scripts/debug_helper.py

# 只检查特定功能
python -c "
from scripts.debug_helper import check_system_resources
check_system_resources()
"
```

## 🛠️ 常见问题解决

### 1. 节点申请失败
```bash
# 检查资源配额
sinfo -p cpu
squeue -u s20223050931

# 调整资源申请
# 在 debug_on_cpu_node.sh 中修改：
# --cpus-per-task=8 --mem=32G --time=02:00:00
```

### 2. 环境激活失败
```bash
# 手动激活环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate amp_esm2

# 检查环境是否存在
conda env list
```

### 3. ESM模型加载失败
```bash
# 检查网络连接
ping huggingface.co

# 手动下载模型
python -c "
import esm
model, alphabet = esm.pretrained.esm2_t6_8M_UR50D()
print('模型下载成功')
"
```

### 4. 内存不足
```bash
# 监控内存使用
free -h
htop

# 使用更小的模型
# 在代码中使用 esm2_t6_8M_UR50D 而不是 esm2_t33_650M_UR50D
```

## 📈 性能优化建议

### 1. CPU优化
```bash
# 环境变量已自动设置
export OMP_NUM_THREADS=$SLURM_CPUS_PER_TASK
export MKL_NUM_THREADS=$SLURM_CPUS_PER_TASK
```

### 2. 内存优化
```python
# 在Python代码中
import gc
import torch

def clear_memory():
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

# 定期调用
clear_memory()
```

### 3. 批处理大小调整
根据调试报告的建议：
- 64GB内存：批处理大小32
- 32GB内存：批处理大小16
- 16GB内存：批处理大小8

## 📋 调试检查清单

### 环境检查
- [ ] SLURM节点成功分配
- [ ] amp_esm2环境成功激活
- [ ] Python版本正确（3.9+）
- [ ] 所有依赖包正常导入

### 资源检查
- [ ] CPU核心数符合预期
- [ ] 内存使用率正常（<80%）
- [ ] 磁盘空间充足（>10GB）

### 模型检查
- [ ] ESM模型成功加载
- [ ] 数据预处理正常
- [ ] 模型推理测试通过

### 代码检查
- [ ] notebook文件完整
- [ ] 配置脚本可执行
- [ ] 调试工具正常运行

## 🎯 调试最佳实践

1. **分步调试**：先验证环境，再测试模型，最后运行完整代码
2. **资源监控**：定期检查CPU和内存使用情况
3. **日志记录**：保存调试过程和错误信息
4. **检查点保存**：在关键步骤保存中间结果
5. **及时清理**：释放不需要的内存和临时文件

## 📞 技术支持

### 调试问题
- 查看调试报告：`debug_report_*.md`
- 运行诊断工具：`python scripts/debug_helper.py`
- 检查SLURM日志：`squeue -u s20223050931`

### 联系方式
- 邮箱：<EMAIL>
- 平台：s20223050931@**************

---

**作者**: ZK  
**日期**: 2025-01-27  
**版本**: 1.0

现在您可以开始在CPU节点上调试您的ESM-2训练脚本了！🧬✨
