# VSCode高算平台集成指南

## 🎯 目标
在VSCode中直接连接到高算平台 `s20223050931@**************`，申请CPU节点并运行Jupyter。

## 🚀 快速开始

### 方法1：VSCode Remote-SSH + 交互式节点（推荐）

#### 第一步：配置SSH连接
1. 在本地创建/编辑 `~/.ssh/config` 文件：
```bash
# 复制我们提供的SSH配置模板
cp configs/ssh_config_template ~/.ssh/config
# 或者手动添加配置内容
```

2. 创建socket目录：
```bash
mkdir -p ~/.ssh/sockets
```

#### 第二步：VSCode连接设置
1. 安装VSCode扩展：
   - Remote - SSH
   - Python
   - Jupyter

2. 在VSCode中按 `Ctrl+Shift+P`，输入 `Remote-SSH: Connect to Host`

3. 选择 `hpc-main` 或直接输入 `s20223050931@**************`

#### 第三步：申请CPU节点并启动Jupyter
连接到高算平台后，在VSCode终端中运行：
```bash
# 进入项目目录
cd ~/amp_project

# 给脚本添加执行权限
chmod +x scripts/vscode_hpc_integration.sh

# 运行集成脚本（会自动申请CPU节点）
./scripts/vscode_hpc_integration.sh
```

#### 第四步：连接Jupyter
脚本运行后会显示连接信息，例如：
```
🌐 连接信息:
  节点IP: *************
  端口: 8888
  完整地址: http://*************:8888
```

在VSCode中：
1. 按 `Ctrl+Shift+P`
2. 输入 `Jupyter: Specify Jupyter Server URI`
3. 输入显示的地址：`http://*************:8888`

### 方法2：SLURM作业提交 + SSH隧道

#### 第一步：提交Jupyter作业
```bash
# 在高算平台上运行
sbatch scripts/run_jupyter_hpc.sh

# 查看作业状态
squeue -u s20223050931

# 查看作业输出获取节点信息
tail -f logs/jupyter_<job_id>.out
```

#### 第二步：建立SSH隧道
在本地终端运行：
```bash
# 从作业日志中获取节点IP，然后建立隧道
ssh -L 8888:节点IP:8888 s20223050931@**************
```

#### 第三步：VSCode连接
在VSCode中连接到 `http://localhost:8888`

## 🔧 高级配置

### 1. VSCode工作区设置
我们已经为您创建了 `.vscode/settings.json`，包含：
- Python解释器路径自动设置
- Jupyter服务器配置
- 远程连接优化
- 终端环境变量

### 2. 自动环境激活
在VSCode终端中，环境会自动激活：
```bash
# 自动执行
source ~/miniconda3/etc/profile.d/conda.sh
conda activate amp_esm2
```

### 3. 端口管理
如果8888端口被占用，脚本会自动寻找可用端口：
- 8888 → 8889 → 8890 → ...

## 📊 资源配置建议

### CPU节点申请参数
```bash
# 在 scripts/vscode_hpc_integration.sh 中可调整
srun --partition=cpu \
     --cpus-per-task=16 \    # CPU核心数
     --mem=64G \             # 内存大小
     --time=08:00:00 \       # 运行时间
     --pty bash
```

### 根据您的需求调整：
- **轻量测试**: `--cpus-per-task=8 --mem=32G --time=04:00:00`
- **正式训练**: `--cpus-per-task=32 --mem=128G --time=24:00:00`

## 🛠️ 常见问题解决

### 1. 连接超时
```bash
# 检查SSH连接
ssh -v s20223050931@**************

# 检查网络连通性
ping **************
```

### 2. 端口被占用
```bash
# 查看端口使用情况
netstat -an | grep 8888

# 手动指定端口
jupyter lab --port=8889
```

### 3. 环境未激活
```bash
# 手动激活环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate amp_esm2

# 验证环境
which python
python -c "import torch; print(torch.__version__)"
```

### 4. VSCode无法连接Jupyter
1. 检查防火墙设置
2. 确认节点IP地址正确
3. 尝试使用SSH隧道方式

## 📋 使用流程总结

### 日常使用流程：
1. **打开VSCode** → Remote-SSH连接到高算平台
2. **运行脚本** → `./scripts/vscode_hpc_integration.sh`
3. **等待节点分配** → 脚本会自动申请CPU节点
4. **连接Jupyter** → 使用显示的地址连接
5. **开始工作** → 运行您的notebook

### 优势：
- ✅ **一键申请节点**：无需手动写SLURM脚本
- ✅ **自动环境配置**：自动激活conda环境
- ✅ **智能端口管理**：自动寻找可用端口
- ✅ **VSCode集成**：无缝的开发体验
- ✅ **资源优化**：根据需求自动配置资源

## 🎯 最佳实践

1. **工作前检查**：
   ```bash
   # 检查资源使用情况
   squeue -u s20223050931
   sinfo -p cpu
   ```

2. **合理申请资源**：
   - 测试阶段：较少的CPU和内存
   - 正式训练：根据数据量调整

3. **及时释放资源**：
   - 完成工作后及时退出节点
   - 使用 `scancel <job_id>` 取消不需要的作业

4. **数据备份**：
   - 定期保存重要结果
   - 使用检查点机制

## 📞 技术支持

- **脚本问题**：检查 `scripts/vscode_hpc_integration.sh`
- **连接问题**：查看SSH配置 `configs/ssh_config_template`
- **环境问题**：运行 `python scripts/verify_esm_compatibility.py`
- **联系邮箱**：<EMAIL>

---

**作者**: ZK  
**平台**: s20223050931@**************  
**日期**: 2025-01-27
