# 高算平台部署指南

## 项目概述
本指南详细说明如何在高算平台上部署和运行ESM-2抗菌肽分类项目。

## 前置条件

### 1. 高算平台要求
- 支持SLURM作业调度系统
- 已安装Conda/Miniconda
- 具备CPU节点访问权限
- 网络连接正常（用于下载模型和依赖）

### 2. 推荐资源配置
```yaml
CPU节点配置:
  - CPU核心: 16-32核
  - 内存: 64GB+
  - 存储: 100GB+
  - 运行时间: 24小时

GPU节点配置（可选）:
  - GPU: NVIDIA V100/A100
  - CPU核心: 8-16核
  - 内存: 32GB+
  - GPU内存: 16GB+
```

## 部署步骤

### 第一步：连接高算平台
```bash
# SSH连接到高算平台
ssh <EMAIL>

# 创建项目目录
mkdir -p ~/amp_project
cd ~/amp_project
```

### 第二步：上传项目文件
```bash
# 方法1：使用scp上传
scp -r /path/to/local/project/* username@hpc-cluster:~/amp_project/

# 方法2：使用rsync同步
rsync -avz --progress /path/to/local/project/ username@hpc-cluster:~/amp_project/

# 方法3：使用git克隆（如果项目在git仓库中）
git clone https://github.com/your-repo/amp-project.git
```

### 第三步：环境配置
```bash
# 给脚本添加执行权限
chmod +x scripts/setup_hpc_environment.sh
chmod +x scripts/run_jupyter_hpc.sh
chmod +x scripts/run_amp_training_cpu.sh

# 运行环境配置脚本
./scripts/setup_hpc_environment.sh
```

### 第四步：验证环境
```bash
# 激活环境
source activate_env.sh

# 运行测试脚本
python test_environment.py
```

### 第五步：启动Jupyter Lab
```bash
# 提交Jupyter Lab作业
sbatch scripts/run_jupyter_hpc.sh

# 查看作业状态
squeue -u $USER

# 查看作业输出（获取访问URL）
tail -f logs/jupyter_<job_id>.out
```

### 第六步：访问Jupyter Lab
1. 从日志文件中获取节点IP和端口
2. 设置SSH隧道：
```bash
# 在本地终端运行
ssh -L 8888:node_ip:8888 username@hpc-cluster
```
3. 在浏览器中访问：`http://localhost:8888`

## 运行实验

### 方法1：交互式运行（推荐用于调试）
1. 在Jupyter Lab中打开 `ESM2_AMP_Training_Complete.ipynb`
2. 逐个运行代码单元
3. 监控训练过程和结果

### 方法2：批处理运行（推荐用于正式训练）
```bash
# 提交训练作业
sbatch scripts/run_amp_training_cpu.sh

# 监控作业状态
squeue -u $USER

# 查看训练日志
tail -f logs/amp_training_<job_id>.out
```

## 常见问题与解决方案

### 1. 环境配置问题
**问题**: Conda环境创建失败
```bash
# 解决方案：清理conda缓存
conda clean --all
conda update conda
```

**问题**: PyTorch安装失败
```bash
# 解决方案：使用pip安装
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

### 2. 内存不足问题
**问题**: ESM-2模型加载时内存不足
```python
# 解决方案：使用较小的ESM-2模型
model, alphabet = esm.pretrained.esm2_t6_8M_UR50D()  # 8M参数
# 而不是
# model, alphabet = esm.pretrained.esm2_t33_650M_UR50D()  # 650M参数
```

### 3. 作业调度问题
**问题**: 作业长时间排队
```bash
# 解决方案：调整资源请求
#SBATCH --cpus-per-task=8    # 减少CPU需求
#SBATCH --mem=32G            # 减少内存需求
#SBATCH --time=12:00:00      # 减少时间需求
```

### 4. 网络连接问题
**问题**: 无法下载ESM-2模型
```bash
# 解决方案：预下载模型文件
wget https://dl.fbaipublicfiles.com/fair-esm/models/esm2_t6_8M_UR50D.pt
# 然后在代码中指定本地路径
```

## 性能优化建议

### 1. CPU优化
```bash
# 设置环境变量
export OMP_NUM_THREADS=$SLURM_CPUS_PER_TASK
export MKL_NUM_THREADS=$SLURM_CPUS_PER_TASK
export NUMEXPR_NUM_THREADS=$SLURM_CPUS_PER_TASK
```

### 2. 内存优化
```python
# 在Python代码中
import torch
torch.set_num_threads(16)  # 设置PyTorch线程数

# 使用梯度累积减少内存使用
accumulation_steps = 4
for i, batch in enumerate(dataloader):
    loss = model(batch) / accumulation_steps
    loss.backward()
    if (i + 1) % accumulation_steps == 0:
        optimizer.step()
        optimizer.zero_grad()
```

### 3. 数据加载优化
```python
# 使用多进程数据加载
dataloader = DataLoader(
    dataset, 
    batch_size=32,
    num_workers=4,  # 根据CPU核心数调整
    pin_memory=True
)
```

## 结果收集

### 1. 训练结果
- 模型文件：`models/`
- 训练日志：`logs/`
- 图表结果：`results/figures/`
- 检查点：`results/checkpoints/`

### 2. 下载结果
```bash
# 从高算平台下载结果
scp -r username@hpc-cluster:~/amp_project/results/ ./local_results/
scp -r username@hpc-cluster:~/amp_project/models/ ./local_models/
```

## 监控和调试

### 1. 实时监控
```bash
# 查看作业状态
squeue -u $USER

# 查看节点资源使用
scontrol show job <job_id>

# 查看实时日志
tail -f logs/amp_training_<job_id>.out
```

### 2. 调试技巧
```bash
# 使用交互式节点调试
srun --pty --cpus-per-task=4 --mem=16G bash

# 在交互式节点中测试代码
conda activate amp_esm2
python test_environment.py
```

## 最佳实践

1. **资源申请**：根据实际需求申请资源，避免浪费
2. **检查点保存**：定期保存训练检查点，防止意外中断
3. **日志记录**：详细记录训练过程，便于问题排查
4. **结果备份**：及时下载重要结果，避免数据丢失
5. **环境管理**：使用conda环境隔离，避免依赖冲突

## 联系支持

如果遇到问题，请：
1. 查看日志文件：`logs/`目录下的相关文件
2. 检查环境配置：运行`python test_environment.py`
3. 联系高算平台技术支持
4. 发送邮件至：<EMAIL>

---

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-01-27  
**版本**: 1.0
