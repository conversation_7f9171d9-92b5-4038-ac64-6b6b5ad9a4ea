# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-20
# 描述: 修复版抗菌肽数据下载器，包含2024年最新的数据库URL

import os
import requests
import pandas as pd
import numpy as np
from Bio import SeqIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord
from datetime import datetime
import hashlib
from tqdm import tqdm
import time
import json
import zipfile
import tempfile

class AMPDataDownloaderFixed:
    """修复版抗菌肽数据下载器 - 2024年最新URL"""
    
    def __init__(self, output_dir, max_retries=3, timeout=60):
        self.output_dir = output_dir
        self.max_retries = max_retries
        self.timeout = timeout
        self.download_log = []
        
        # 2024年最新数据库配置
        self.database_config = {
            'dbAMP_3.0': {
                'url': 'https://awi.cuhk.edu.cn/dbAMP/download/3.0/activity/dbAMP_Antimicrobial.fasta',
                'backup_url': 'https://awi.cuhk.edu.cn/dbAMP/download/3.0/activity/dbAMP_Antibacterial.fasta',
                'description': 'dbAMP 3.0 抗菌活性肽数据库 (2024年6月发布)',
                'expected_format': 'fasta'
            },
            'DRAMP_general': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/general_amps.fasta',
                'description': 'DRAMP 4.0 通用抗菌肽数据库 (30260条记录)',
                'expected_format': 'fasta'
            },
            'DRAMP_antimicrobial': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/Antimicrobial_amps.fasta',
                'description': 'DRAMP 4.0 抗菌活性肽数据库',
                'expected_format': 'fasta'
            },
            'DRAMP_antibacterial': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/Antibacterial_amps.fasta',
                'description': 'DRAMP 4.0 抗细菌肽数据库',
                'expected_format': 'fasta'
            },
            'DRAMP_natural': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/natural_amps.fasta',
                'description': 'DRAMP 4.0 天然抗菌肽数据库',
                'expected_format': 'fasta'
            }
        }
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
    def calculate_file_hash(self, filepath):
        """计算文件的SHA256哈希值"""
        hash_sha256 = hashlib.sha256()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def download_file(self, url, filename, description=""):
        """下载单个文件，包含重试机制和更好的错误处理"""
        filepath = os.path.join(self.output_dir, filename)
        
        for attempt in range(self.max_retries):
            try:
                print(f"正在下载 {description} (尝试 {attempt + 1}/{self.max_retries})...")
                print(f"URL: {url}")
                
                # 设置请求头，模拟浏览器访问
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
                
                response = requests.get(url, timeout=self.timeout, stream=True, headers=headers)
                response.raise_for_status()
                
                # 检查响应内容类型
                content_type = response.headers.get('content-type', '').lower()
                print(f"响应类型: {content_type}")
                
                # 获取文件大小
                total_size = int(response.headers.get('content-length', 0))
                
                with open(filepath, 'wb') as f, tqdm(
                    desc=filename,
                    total=total_size,
                    unit='B',
                    unit_scale=True,
                    unit_divisor=1024,
                ) as pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            pbar.update(len(chunk))
                
                # 验证文件完整性
                file_size = os.path.getsize(filepath)
                if file_size == 0:
                    raise ValueError("下载的文件为空")
                
                # 验证FASTA格式（如果是FASTA文件）
                if filename.endswith('.fasta') or filename.endswith('.fa'):
                    try:
                        records = list(SeqIO.parse(filepath, "fasta"))
                        if len(records) == 0:
                            raise ValueError("FASTA文件中没有有效序列")
                        print(f"✅ FASTA验证通过，包含 {len(records)} 个序列")
                    except Exception as e:
                        print(f"⚠️ FASTA格式验证失败: {e}")
                        # 检查文件内容
                        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                            content_preview = f.read(500)
                            print(f"文件内容预览: {content_preview[:200]}...")
                
                file_hash = self.calculate_file_hash(filepath)
                
                # 记录下载信息
                download_info = {
                    'filename': filename,
                    'url': url,
                    'description': description,
                    'download_time': datetime.now().isoformat(),
                    'file_size': file_size,
                    'sha256_hash': file_hash,
                    'status': 'success',
                    'content_type': content_type
                }
                self.download_log.append(download_info)
                
                print(f"✅ 成功下载 {filename} ({file_size:,} bytes)")
                return True
                
            except requests.exceptions.RequestException as e:
                print(f"❌ 网络请求失败 (尝试 {attempt + 1}): {str(e)}")
            except Exception as e:
                print(f"❌ 下载失败 (尝试 {attempt + 1}): {str(e)}")
            
            if attempt < self.max_retries - 1:
                wait_time = 2 ** attempt
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                # 记录失败信息
                download_info = {
                    'filename': filename,
                    'url': url,
                    'description': description,
                    'download_time': datetime.now().isoformat(),
                    'error': str(e),
                    'status': 'failed'
                }
                self.download_log.append(download_info)
        
        return False
    
    def download_all_databases(self):
        """下载所有配置的数据库"""
        print("🚀 开始下载抗菌肽数据库...")
        print("=" * 60)
        
        for db_name, db_config in self.database_config.items():
            print(f"\n📥 下载 {db_name}...")
            print(f"描述: {db_config['description']}")
            
            filename = f"{db_name}_{datetime.now().strftime('%Y%m%d')}.fasta"
            
            success = self.download_file(
                url=db_config['url'],
                filename=filename,
                description=db_config['description']
            )
            
            if not success and 'backup_url' in db_config:
                print("尝试备用URL...")
                success = self.download_file(
                    url=db_config['backup_url'],
                    filename=f"{db_name}_backup_{datetime.now().strftime('%Y%m%d')}.fasta",
                    description=f"{db_config['description']} (备用源)"
                )
            
            if success:
                print(f"✅ {db_name} 下载完成")
            else:
                print(f"❌ {db_name} 下载失败")
            
            print("-" * 40)
    
    def save_download_log(self):
        """保存下载日志"""
        log_file = os.path.join(self.output_dir, f'download_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.download_log, f, ensure_ascii=False, indent=2)
        print(f"📋 下载日志已保存到: {log_file}")
    
    def generate_summary_report(self):
        """生成下载总结报告"""
        successful_downloads = [log for log in self.download_log if log['status'] == 'success']
        failed_downloads = [log for log in self.download_log if log['status'] == 'failed']
        
        print("\n" + "=" * 60)
        print("📊 数据下载总结报告")
        print("=" * 60)
        print(f"成功下载: {len(successful_downloads)} 个文件")
        print(f"下载失败: {len(failed_downloads)} 个文件")
        
        if successful_downloads:
            total_size = sum(log['file_size'] for log in successful_downloads)
            print(f"总数据量: {total_size / (1024 * 1024):.2f} MB")
            
            print("\n✅ 成功下载的文件:")
            for log in successful_downloads:
                print(f"  - {log['filename']}: {log['file_size']:,} bytes")
        
        if failed_downloads:
            print("\n❌ 失败的下载:")
            for log in failed_downloads:
                print(f"  - {log['filename']}: {log.get('error', '未知错误')}")
        
        print(f"\n📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    # 设置输出目录
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    output_dir = os.path.join(project_root, 'data', 'raw')
    
    # 创建下载器
    downloader = AMPDataDownloaderFixed(output_dir)
    
    # 下载所有数据库
    downloader.download_all_databases()
    
    # 保存日志和生成报告
    downloader.save_download_log()
    downloader.generate_summary_report()
    
    print("\n🎉 数据下载流程完成！")
    print("\n📝 下一步: 运行 02_数据清洗与预处理.ipynb")

if __name__ == "__main__":
    main()
