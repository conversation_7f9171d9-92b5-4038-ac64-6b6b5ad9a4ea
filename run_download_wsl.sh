#!/bin/bash
# WSL环境下运行抗菌肽数据下载脚本
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-20

set -e  # 遇到错误立即退出

echo "🚀 在WSL环境中运行抗菌肽数据下载..."
echo "=================================="

# 检查当前目录
echo "📁 当前目录: $(pwd)"

# 激活conda环境
echo "🔄 激活conda环境: amp_esm2"
source ~/miniconda3/etc/profile.d/conda.sh
conda activate amp_esm2

# 验证环境
echo "🔍 验证Python环境..."
echo "Python版本: $(python --version)"
echo "Python路径: $(which python)"

# 检查依赖包
echo "📦 检查依赖包..."
python -c "
import sys
print(f'Python路径: {sys.executable}')

try:
    import requests
    import pandas
    import numpy
    from Bio import SeqIO
    from tqdm import tqdm
    print('✅ 所有必要的依赖包都已安装')
except ImportError as e:
    print(f'❌ 缺少依赖包: {e}')
    sys.exit(1)
"

# 检查项目结构
echo "📂 检查项目结构..."
if [ ! -d "data" ]; then
    echo "创建data目录..."
    mkdir -p data/raw data/processed data/analysis_ready
fi

if [ ! -d "scripts/data_download" ]; then
    echo "❌ 错误: scripts/data_download目录不存在"
    exit 1
fi

# 运行数据下载脚本
echo "📥 开始运行数据下载脚本..."
echo "=================================="

python scripts/data_download/amp_data_downloader_fixed.py

echo ""
echo "🎉 数据下载脚本执行完成！"
echo "=================================="

# 检查下载结果
echo "📊 检查下载结果..."
if [ -d "data/raw" ]; then
    echo "data/raw目录内容:"
    ls -la data/raw/
    
    echo ""
    echo "下载的文件统计:"
    find data/raw -name "*.fasta" -exec wc -l {} + 2>/dev/null || echo "没有找到FASTA文件"
else
    echo "⚠️ data/raw目录不存在"
fi

echo ""
echo "✅ WSL环境下载流程完成！"
