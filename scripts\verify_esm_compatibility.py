#!/usr/bin/env python3
"""
ESM官网兼容性验证脚本
验证我们的方法与ESM官网推荐方法的兼容性
作者: ZK
邮箱: <EMAIL>
日期: 2025-01-27
"""

import torch
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def test_esm_official_method():
    """测试ESM官网推荐的方法"""
    print("🧪 测试ESM官网推荐方法...")
    
    try:
        # 1. 按官网方式导入
        import esm
        print("✅ ESM库导入成功")
        
        # 2. 按官网方式加载模型
        print("📥 加载ESM-2模型...")
        model, alphabet = esm.pretrained.esm2_t6_8M_UR50D()  # 使用最小模型测试
        batch_converter = alphabet.get_batch_converter()
        model.eval()  # disables dropout for deterministic results
        print("✅ 模型加载成功")
        
        # 3. 按官网方式准备数据
        print("📊 准备测试数据...")
        data = [
            ("protein1", "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"),
            ("protein2", "KALTARQQEVFDLIRDHISQTGMPPTRAEIAQRLGFRSPNAAEEHLKALARKGVIEIVSGASRGIRLLQEE"),
            ("protein2 with mask","KALTARQQEVFDLIRD<mask>ISQTGMPPTRAEIAQRLGFRSPNAAEEHLKALARKGVIEIVSGASRGIRLLQEE"),
            ("protein3",  "K A <mask> I S Q"),
        ]
        batch_labels, batch_strs, batch_tokens = batch_converter(data)
        batch_lens = (batch_tokens != alphabet.padding_idx).sum(1)
        print("✅ 数据准备成功")
        
        # 4. 按官网方式进行推理
        print("🔮 执行模型推理...")
        with torch.no_grad():
            results = model(batch_tokens, repr_layers=[6], return_contacts=True)  # 使用最后一层
        token_representations = results["representations"][6]
        print("✅ 模型推理成功")
        
        # 5. 按官网方式生成序列表示
        print("🧬 生成序列表示...")
        sequence_representations = []
        for i, tokens_len in enumerate(batch_lens):
            sequence_representations.append(token_representations[i, 1 : tokens_len - 1].mean(0))
        print("✅ 序列表示生成成功")
        
        # 6. 验证输出格式
        print("🔍 验证输出格式...")
        print(f"   - 批次大小: {len(sequence_representations)}")
        print(f"   - 表示维度: {sequence_representations[0].shape}")
        print(f"   - 注意力矩阵形状: {results['contacts'][0].shape}")
        print("✅ 输出格式验证通过")
        
        # 清理内存
        del model, alphabet, results, token_representations
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        return True
        
    except Exception as e:
        print(f"❌ ESM官网方法测试失败: {e}")
        return False

def test_our_current_method():
    """测试我们当前的方法"""
    print("\n🧪 测试我们当前的方法...")
    
    try:
        # 模拟我们notebook中的使用方式
        import esm
        
        # 使用我们的自适应模型选择
        def select_esm_model_adaptive():
            """自适应选择ESM模型"""
            import psutil
            available_memory = psutil.virtual_memory().available / (1024**3)
            
            if available_memory > 40:
                return 'esm2_t33_650M_UR50D'
            elif available_memory > 20:
                return 'esm2_t12_35M_UR50D'
            else:
                return 'esm2_t6_8M_UR50D'
        
        model_name = select_esm_model_adaptive()
        print(f"📥 自适应选择模型: {model_name}")
        
        # 加载模型
        model, alphabet = getattr(esm.pretrained, model_name)()
        model.eval()
        print("✅ 自适应模型加载成功")
        
        # 测试数据处理
        batch_converter = alphabet.get_batch_converter()
        test_data = [("test", "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG")]
        batch_labels, batch_strs, batch_tokens = batch_converter(test_data)
        
        # 推理
        with torch.no_grad():
            # 根据模型选择合适的层数
            if '650M' in model_name:
                repr_layers = [33]
            elif '35M' in model_name:
                repr_layers = [12]
            else:
                repr_layers = [6]
                
            results = model(batch_tokens, repr_layers=repr_layers, return_contacts=True)
        
        print("✅ 我们的方法测试成功")
        
        # 清理内存
        del model, alphabet, results
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        return True
        
    except Exception as e:
        print(f"❌ 我们的方法测试失败: {e}")
        return False

def compare_methods():
    """对比两种方法"""
    print("\n📊 方法对比分析:")
    print("=" * 60)
    
    print("🔍 相同点:")
    print("  ✅ 模型加载方式: esm.pretrained.xxx() - 完全一致")
    print("  ✅ 数据预处理: alphabet.get_batch_converter() - 完全一致")
    print("  ✅ 推理方式: model(tokens, repr_layers=[], return_contacts=True) - 完全一致")
    print("  ✅ 表示提取: results['representations'][layer] - 完全一致")
    
    print("\n🔧 我们的优势:")
    print("  🎯 自适应模型选择: 根据可用内存自动选择合适的模型大小")
    print("  💾 内存管理: 自动内存监控和清理")
    print("  🔄 检查点管理: 完整的训练检查点保存和恢复")
    print("  📊 批处理优化: 根据内存自动调整批处理大小")
    print("  🖥️  高算平台优化: 专门针对SLURM环境优化")
    
    print("\n📋 建议调整:")
    print("  1. 更新requirements.txt使用 'fair-esm' (不指定版本)")
    print("  2. 保持我们的自适应选择逻辑")
    print("  3. 确保与官网示例代码的兼容性")

def main():
    """主函数"""
    print("🚀 ESM兼容性验证开始...")
    print("=" * 60)
    
    # 测试官网方法
    official_success = test_esm_official_method()
    
    # 测试我们的方法
    our_success = test_our_current_method()
    
    # 对比分析
    compare_methods()
    
    print("\n" + "=" * 60)
    print("📋 验证结果总结:")
    print(f"  官网方法: {'✅ 通过' if official_success else '❌ 失败'}")
    print(f"  我们的方法: {'✅ 通过' if our_success else '❌ 失败'}")
    
    if official_success and our_success:
        print("\n🎉 兼容性验证完全通过!")
        print("💡 我们的方法与ESM官网推荐方法完全兼容，并且提供了额外的优化功能")
    else:
        print("\n⚠️  存在兼容性问题，需要进一步调试")
    
    print("\n🔗 ESM官网链接: https://github.com/facebookresearch/esm")
    print("📖 我们的方法在官网基础上增加了高算平台优化和自适应配置")

if __name__ == "__main__":
    main()
