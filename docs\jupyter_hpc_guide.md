# 高算平台Jupyter运行指南

## 项目概述
本指南专门说明如何在高算平台上以Jupyter Notebook形式运行ESM-2抗菌肽分类项目。

## 快速开始

### 1. 连接高算平台并上传项目
```bash
# SSH连接
ssh <EMAIL>

# 创建项目目录
mkdir -p ~/amp_project
cd ~/amp_project

# 上传项目文件（在本地执行）
scp -r /path/to/local/project/* username@hpc-cluster:~/amp_project/
```

### 2. 环境配置
```bash
# 给脚本添加执行权限
chmod +x scripts/setup_hpc_environment.sh
chmod +x scripts/run_jupyter_hpc.sh

# 运行环境配置
./scripts/setup_hpc_environment.sh

# 验证环境
source activate_env.sh
python test_environment.py
```

### 3. 启动Jupyter Lab
```bash
# 提交Jupyter作业
sbatch scripts/run_jupyter_hpc.sh

# 查看作业状态
squeue -u $USER

# 查看作业输出获取访问信息
tail -f logs/jupyter_<job_id>.out
```

### 4. 建立SSH隧道访问
在本地终端执行：
```bash
# 从日志中获取节点IP和端口，然后建立隧道
ssh -L 8888:node_ip:8888 username@hpc-cluster

# 在浏览器中访问
http://localhost:8888
```

## ESM-2 Notebook优化配置

### 1. 内存管理
在notebook开始添加以下代码：
```python
import torch
import gc
import os

# 设置PyTorch线程数
torch.set_num_threads(int(os.environ.get('SLURM_CPUS_PER_TASK', '16')))

# 内存清理函数
def clear_memory():
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
# 在每个大的计算块后调用
clear_memory()
```

### 2. 批处理大小调整
```python
# 根据可用内存调整批处理大小
import psutil

def get_optimal_batch_size():
    """根据可用内存计算最优批处理大小"""
    available_memory = psutil.virtual_memory().available / (1024**3)  # GB
    
    if available_memory > 50:
        return 32
    elif available_memory > 30:
        return 16
    elif available_memory > 20:
        return 8
    else:
        return 4

BATCH_SIZE = get_optimal_batch_size()
print(f"🔧 使用批处理大小: {BATCH_SIZE}")
```

### 3. 模型加载优化
```python
# 使用较小的ESM-2模型进行测试
import esm

# 选择合适的模型大小
def load_esm_model(model_size='small'):
    """加载ESM-2模型"""
    if model_size == 'small':
        model, alphabet = esm.pretrained.esm2_t6_8M_UR50D()
        print("✅ 加载ESM-2 8M模型（适合CPU训练）")
    elif model_size == 'medium':
        model, alphabet = esm.pretrained.esm2_t12_35M_UR50D()
        print("✅ 加载ESM-2 35M模型")
    elif model_size == 'large':
        model, alphabet = esm.pretrained.esm2_t33_650M_UR50D()
        print("✅ 加载ESM-2 650M模型（需要大内存）")
    
    return model, alphabet

# 根据资源选择模型
model, alphabet = load_esm_model('small')  # 推荐从small开始
```

### 4. 训练进度监控
```python
from tqdm.notebook import tqdm
import matplotlib.pyplot as plt

# 使用notebook专用的进度条
def train_with_progress(model, dataloader, epochs=10):
    """带进度条的训练函数"""
    losses = []
    
    for epoch in tqdm(range(epochs), desc="训练进度"):
        epoch_losses = []
        
        for batch in tqdm(dataloader, desc=f"Epoch {epoch+1}", leave=False):
            # 训练代码
            loss = train_step(model, batch)
            epoch_losses.append(loss)
            
            # 实时显示损失
            if len(epoch_losses) % 10 == 0:
                avg_loss = sum(epoch_losses) / len(epoch_losses)
                tqdm.write(f"当前平均损失: {avg_loss:.4f}")
        
        losses.append(sum(epoch_losses) / len(epoch_losses))
        
        # 实时绘制损失曲线
        if epoch % 5 == 0:
            plt.figure(figsize=(10, 4))
            plt.plot(losses)
            plt.title('训练损失曲线')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.show()
    
    return losses
```

### 5. 检查点保存
```python
import pickle
from pathlib import Path

# 创建检查点目录
checkpoint_dir = Path("results/checkpoints")
checkpoint_dir.mkdir(parents=True, exist_ok=True)

def save_checkpoint(model, optimizer, epoch, loss, filename=None):
    """保存训练检查点"""
    if filename is None:
        filename = f"checkpoint_epoch_{epoch}.pth"
    
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss,
        'timestamp': time.time()
    }
    
    torch.save(checkpoint, checkpoint_dir / filename)
    print(f"✅ 检查点已保存: {filename}")

def load_checkpoint(model, optimizer, filename):
    """加载训练检查点"""
    checkpoint = torch.load(checkpoint_dir / filename)
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    return checkpoint['epoch'], checkpoint['loss']
```

## 常见问题解决

### 1. 内存不足
```python
# 在notebook中监控内存使用
import psutil

def monitor_memory():
    """监控内存使用情况"""
    memory = psutil.virtual_memory()
    print(f"内存使用: {memory.percent}%")
    print(f"可用内存: {memory.available / (1024**3):.1f} GB")
    
    if memory.percent > 90:
        print("⚠️  内存使用过高，建议清理内存")
        clear_memory()

# 在训练过程中定期调用
monitor_memory()
```

### 2. 训练中断恢复
```python
# 在训练开始前检查是否有检查点
import glob

def resume_training():
    """恢复训练"""
    checkpoints = glob.glob("results/checkpoints/checkpoint_epoch_*.pth")
    
    if checkpoints:
        # 找到最新的检查点
        latest_checkpoint = max(checkpoints, key=os.path.getctime)
        print(f"🔄 发现检查点: {latest_checkpoint}")
        
        # 加载检查点
        start_epoch, last_loss = load_checkpoint(model, optimizer, 
                                                os.path.basename(latest_checkpoint))
        print(f"📊 从第 {start_epoch} 轮继续训练，上次损失: {last_loss:.4f}")
        return start_epoch
    else:
        print("🆕 开始新的训练")
        return 0

start_epoch = resume_training()
```

### 3. 结果可视化
```python
# 实时结果展示
%matplotlib inline
import matplotlib.pyplot as plt
import seaborn as sns

def plot_training_results(history):
    """绘制训练结果"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 损失曲线
    axes[0, 0].plot(history['train_loss'], label='训练损失')
    axes[0, 0].plot(history['val_loss'], label='验证损失')
    axes[0, 0].set_title('损失曲线')
    axes[0, 0].legend()
    
    # 准确率曲线
    axes[0, 1].plot(history['train_acc'], label='训练准确率')
    axes[0, 1].plot(history['val_acc'], label='验证准确率')
    axes[0, 1].set_title('准确率曲线')
    axes[0, 1].legend()
    
    # AUC曲线
    axes[1, 0].plot(history['val_auc'], label='验证AUC')
    axes[1, 0].set_title('AUC曲线')
    axes[1, 0].legend()
    
    # 学习率曲线
    axes[1, 1].plot(history['learning_rates'])
    axes[1, 1].set_title('学习率变化')
    axes[1, 1].set_yscale('log')
    
    plt.tight_layout()
    plt.savefig('results/figures/training_progress.png', dpi=300, bbox_inches='tight')
    plt.show()
```

## 最佳实践

### 1. 分阶段运行
- 先运行数据加载和预处理部分
- 然后运行模型定义和小批量测试
- 最后运行完整训练

### 2. 定期保存
- 每5-10个epoch保存一次检查点
- 保存中间结果和图表
- 定期清理内存

### 3. 监控资源
- 使用`htop`或`nvidia-smi`监控资源使用
- 在notebook中添加内存监控代码
- 设置合理的批处理大小

### 4. 错误处理
```python
try:
    # 训练代码
    result = train_model()
except Exception as e:
    print(f"❌ 训练出错: {e}")
    # 保存当前状态
    save_checkpoint(model, optimizer, current_epoch, current_loss, "error_checkpoint.pth")
    raise e
```

## 联系支持
- 邮箱: <EMAIL>
- 查看日志: `logs/jupyter_<job_id>.out`
- 监控作业: `squeue -u $USER`

---
**作者**: ZK  
**日期**: 2025-01-27
