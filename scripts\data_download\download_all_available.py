# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-20
# 描述: 下载所有可用的抗菌肽序列并评估训练适用性

import os
import requests
import pandas as pd
import numpy as np
from Bio import SeqIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord
from datetime import datetime
import hashlib
from tqdm import tqdm
import time
import json
import re
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns

class AllAvailableAMPDownloader:
    """下载所有可用的抗菌肽序列"""
    
    def __init__(self, output_dir):
        self.output_dir = output_dir
        self.download_log = []
        self.sequence_stats = {}
        
        # 扩展的数据库配置 - 包含更多可下载的数据库
        self.database_config = {
            # dbAMP 3.0 - 多个活性类别
            'dbAMP_antimicrobial': {
                'url': 'https://awi.cuhk.edu.cn/dbAMP/download/3.0/activity/dbAMP_Antimicrobial.fasta',
                'description': 'dbAMP 3.0 抗菌活性肽',
                'category': 'antimicrobial'
            },
            'dbAMP_antibacterial': {
                'url': 'https://awi.cuhk.edu.cn/dbAMP/download/3.0/activity/dbAMP_Antibacterial.fasta',
                'description': 'dbAMP 3.0 抗细菌肽',
                'category': 'antibacterial'
            },
            'dbAMP_antifungal': {
                'url': 'https://awi.cuhk.edu.cn/dbAMP/download/3.0/activity/dbAMP_Antifungal.fasta',
                'description': 'dbAMP 3.0 抗真菌肽',
                'category': 'antifungal'
            },
            'dbAMP_antiviral': {
                'url': 'https://awi.cuhk.edu.cn/dbAMP/download/3.0/activity/dbAMP_Antiviral.fasta',
                'description': 'dbAMP 3.0 抗病毒肽',
                'category': 'antiviral'
            },
            
            # DRAMP 4.0 - 多个子集
            'DRAMP_general': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/general_amps.fasta',
                'description': 'DRAMP 4.0 通用抗菌肽',
                'category': 'general'
            },
            'DRAMP_antimicrobial': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/Antimicrobial_amps.fasta',
                'description': 'DRAMP 4.0 抗菌活性肽',
                'category': 'antimicrobial'
            },
            'DRAMP_antibacterial': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/Antibacterial_amps.fasta',
                'description': 'DRAMP 4.0 抗细菌肽',
                'category': 'antibacterial'
            },
            'DRAMP_antifungal': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/Antifungal_amps.fasta',
                'description': 'DRAMP 4.0 抗真菌肽',
                'category': 'antifungal'
            },
            'DRAMP_antiviral': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/Antiviral_amps.fasta',
                'description': 'DRAMP 4.0 抗病毒肽',
                'category': 'antiviral'
            },
            'DRAMP_natural': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/natural_amps.fasta',
                'description': 'DRAMP 4.0 天然抗菌肽',
                'category': 'natural'
            },
            'DRAMP_synthetic': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/synthetic_amps.fasta',
                'description': 'DRAMP 4.0 合成抗菌肽',
                'category': 'synthetic'
            }
        }
        
        os.makedirs(output_dir, exist_ok=True)
    
    def download_file(self, url, filename, description=""):
        """下载文件"""
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            print(f"📥 下载 {description}...")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, timeout=60, stream=True, headers=headers)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f, tqdm(
                desc=filename,
                total=total_size,
                unit='B',
                unit_scale=True,
                unit_divisor=1024,
            ) as pbar:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        pbar.update(len(chunk))
            
            # 验证文件
            file_size = os.path.getsize(filepath)
            if file_size == 0:
                raise ValueError("文件为空")
            
            print(f"✅ 成功下载 {filename} ({file_size:,} bytes)")
            return True
            
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            return False
    
    def analyze_sequences(self, filepath, db_name):
        """分析序列质量和特征"""
        try:
            sequences = []
            sequence_lengths = []
            invalid_sequences = 0
            
            # 读取序列
            for record in SeqIO.parse(filepath, "fasta"):
                seq_str = str(record.seq).upper()
                sequences.append(seq_str)
                sequence_lengths.append(len(seq_str))
                
                # 检查是否包含无效字符
                if not re.match(r'^[ACDEFGHIKLMNPQRSTVWYXBZ]+$', seq_str):
                    invalid_sequences += 1
            
            # 计算统计信息
            stats = {
                'database': db_name,
                'total_sequences': len(sequences),
                'valid_sequences': len(sequences) - invalid_sequences,
                'invalid_sequences': invalid_sequences,
                'min_length': min(sequence_lengths) if sequence_lengths else 0,
                'max_length': max(sequence_lengths) if sequence_lengths else 0,
                'mean_length': np.mean(sequence_lengths) if sequence_lengths else 0,
                'median_length': np.median(sequence_lengths) if sequence_lengths else 0,
                'std_length': np.std(sequence_lengths) if sequence_lengths else 0,
                'sequences_under_50': sum(1 for l in sequence_lengths if l < 50),
                'sequences_under_100': sum(1 for l in sequence_lengths if l < 100),
                'sequences_over_100': sum(1 for l in sequence_lengths if l > 100),
                'training_suitable': 0  # 将在后面计算
            }
            
            # 计算适合训练的序列数量（长度10-100，有效字符）
            training_suitable = 0
            for i, seq in enumerate(sequences):
                length = sequence_lengths[i]
                if (10 <= length <= 100 and 
                    re.match(r'^[ACDEFGHIKLMNPQRSTVWY]+$', seq)):
                    training_suitable += 1
            
            stats['training_suitable'] = training_suitable
            stats['training_ratio'] = training_suitable / len(sequences) if sequences else 0
            
            self.sequence_stats[db_name] = stats
            
            print(f"📊 {db_name} 分析结果:")
            print(f"   总序列数: {stats['total_sequences']:,}")
            print(f"   有效序列: {stats['valid_sequences']:,}")
            print(f"   适合训练: {stats['training_suitable']:,} ({stats['training_ratio']*100:.1f}%)")
            print(f"   长度范围: {stats['min_length']}-{stats['max_length']} (平均: {stats['mean_length']:.1f})")
            print(f"   <100 AA: {stats['sequences_under_100']:,} ({stats['sequences_under_100']/stats['total_sequences']*100:.1f}%)")
            
            return True
            
        except Exception as e:
            print(f"❌ 序列分析失败: {e}")
            return False
    
    def download_all_databases(self):
        """下载所有数据库"""
        print("🚀 开始下载所有可用的抗菌肽数据库...")
        print("=" * 70)
        
        successful_downloads = 0
        
        for db_name, config in self.database_config.items():
            print(f"\n📥 处理 {db_name}...")
            
            filename = f"{db_name}_{datetime.now().strftime('%Y%m%d')}.fasta"
            
            # 下载文件
            success = self.download_file(
                url=config['url'],
                filename=filename,
                description=config['description']
            )
            
            if success:
                # 分析序列
                filepath = os.path.join(self.output_dir, filename)
                analysis_success = self.analyze_sequences(filepath, db_name)
                
                if analysis_success:
                    successful_downloads += 1
                    
                    # 记录下载信息
                    self.download_log.append({
                        'database': db_name,
                        'filename': filename,
                        'description': config['description'],
                        'category': config['category'],
                        'download_time': datetime.now().isoformat(),
                        'status': 'success'
                    })
            else:
                self.download_log.append({
                    'database': db_name,
                    'filename': filename,
                    'description': config['description'],
                    'category': config['category'],
                    'download_time': datetime.now().isoformat(),
                    'status': 'failed'
                })
            
            print("-" * 50)
        
        print(f"\n✅ 成功下载并分析了 {successful_downloads} 个数据库")
        return successful_downloads
    
    def generate_training_suitability_report(self):
        """生成训练适用性报告"""
        print("\n" + "=" * 70)
        print("🎯 模型训练适用性分析报告")
        print("=" * 70)
        
        if not self.sequence_stats:
            print("❌ 没有可分析的数据")
            return
        
        # 汇总统计
        total_sequences = sum(stats['total_sequences'] for stats in self.sequence_stats.values())
        total_valid = sum(stats['valid_sequences'] for stats in self.sequence_stats.values())
        total_training_suitable = sum(stats['training_suitable'] for stats in self.sequence_stats.values())
        
        print(f"📊 总体统计:")
        print(f"   总序列数: {total_sequences:,}")
        print(f"   有效序列: {total_valid:,} ({total_valid/total_sequences*100:.1f}%)")
        print(f"   适合训练: {total_training_suitable:,} ({total_training_suitable/total_sequences*100:.1f}%)")
        
        # 按数据库分类
        print(f"\n📋 各数据库训练适用性:")
        print(f"{'数据库':<25} {'总数':<8} {'适合训练':<10} {'比例':<8} {'推荐':<6}")
        print("-" * 65)
        
        for db_name, stats in self.sequence_stats.items():
            ratio = stats['training_ratio'] * 100
            recommendation = "✅" if ratio > 80 else "⚠️" if ratio > 50 else "❌"
            
            print(f"{db_name:<25} {stats['total_sequences']:<8,} {stats['training_suitable']:<10,} {ratio:<7.1f}% {recommendation:<6}")
        
        # 训练建议
        print(f"\n💡 训练建议:")
        
        high_quality_dbs = [name for name, stats in self.sequence_stats.items() 
                           if stats['training_ratio'] > 0.8]
        medium_quality_dbs = [name for name, stats in self.sequence_stats.items() 
                             if 0.5 < stats['training_ratio'] <= 0.8]
        low_quality_dbs = [name for name, stats in self.sequence_stats.items() 
                          if stats['training_ratio'] <= 0.5]
        
        if high_quality_dbs:
            print(f"   ✅ 高质量数据库 (>80%适用): {len(high_quality_dbs)}个")
            for db in high_quality_dbs[:3]:  # 显示前3个
                print(f"      - {db}")
        
        if medium_quality_dbs:
            print(f"   ⚠️ 中等质量数据库 (50-80%适用): {len(medium_quality_dbs)}个")
            print(f"      建议: 需要额外的数据清洗")
        
        if low_quality_dbs:
            print(f"   ❌ 低质量数据库 (<50%适用): {len(low_quality_dbs)}个")
            print(f"      建议: 谨慎使用或排除")
        
        # 长度分布分析
        print(f"\n📏 序列长度分布分析:")
        under_50_total = sum(stats['sequences_under_50'] for stats in self.sequence_stats.values())
        under_100_total = sum(stats['sequences_under_100'] for stats in self.sequence_stats.values())
        over_100_total = sum(stats['sequences_over_100'] for stats in self.sequence_stats.values())
        
        print(f"   <50 AA:  {under_50_total:,} ({under_50_total/total_sequences*100:.1f}%)")
        print(f"   <100 AA: {under_100_total:,} ({under_100_total/total_sequences*100:.1f}%)")
        print(f"   >100 AA: {over_100_total:,} ({over_100_total/total_sequences*100:.1f}%)")
        
        # 与参考研究对比
        print(f"\n🔍 与参考研究对比:")
        print(f"   参考研究: 24,766个序列 (83%<50 AA)")
        print(f"   我们的数据: {total_training_suitable:,}个适合训练的序列")
        
        if total_training_suitable >= 24766:
            print(f"   ✅ 数量充足，超过参考研究标准")
        else:
            print(f"   ⚠️ 数量不足，建议补充APD和CAMPR4数据")
        
        return {
            'total_sequences': total_sequences,
            'training_suitable': total_training_suitable,
            'high_quality_dbs': high_quality_dbs,
            'recommendation': 'sufficient' if total_training_suitable >= 20000 else 'need_more'
        }
    
    def save_analysis_results(self):
        """保存分析结果"""
        # 保存详细统计
        stats_file = os.path.join(self.output_dir, f'sequence_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        analysis_data = {
            'analysis_timestamp': datetime.now().isoformat(),
            'database_stats': self.sequence_stats,
            'download_log': self.download_log,
            'summary': {
                'total_databases': len(self.database_config),
                'successful_downloads': len([log for log in self.download_log if log['status'] == 'success']),
                'total_sequences': sum(stats['total_sequences'] for stats in self.sequence_stats.values()),
                'training_suitable_sequences': sum(stats['training_suitable'] for stats in self.sequence_stats.values())
            }
        }
        
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, ensure_ascii=False, indent=2)
        
        print(f"📋 详细分析结果已保存到: {stats_file}")

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    output_dir = os.path.join(project_root, 'data', 'raw')
    
    # 创建下载器
    downloader = AllAvailableAMPDownloader(output_dir)
    
    # 下载所有数据库
    success_count = downloader.download_all_databases()
    
    if success_count > 0:
        # 生成训练适用性报告
        report = downloader.generate_training_suitability_report()
        
        # 保存分析结果
        downloader.save_analysis_results()
        
        print(f"\n🎉 数据下载和分析完成！")
        print(f"📊 成功处理 {success_count} 个数据库")
        print(f"🎯 训练适用序列: {report.get('training_suitable', 0):,}个")
        
        if report.get('recommendation') == 'sufficient':
            print(f"✅ 数据量充足，可以开始模型训练")
        else:
            print(f"⚠️ 建议补充更多数据源")
    else:
        print(f"❌ 没有成功下载任何数据库")

if __name__ == "__main__":
    main()
