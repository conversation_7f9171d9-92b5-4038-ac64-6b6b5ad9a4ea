@echo off
chcp 65001 >nul
echo 🔧 VSCode高算平台自动配置 (Windows版本)
echo 平台: s20223050931@**************
echo ==========================================

:: 检查SSH目录
echo ℹ️  检查SSH配置目录...
if not exist "%USERPROFILE%\.ssh" (
    mkdir "%USERPROFILE%\.ssh"
    echo ✅ 创建SSH目录: %USERPROFILE%\.ssh
)

if not exist "%USERPROFILE%\.ssh\sockets" (
    mkdir "%USERPROFILE%\.ssh\sockets"
    echo ✅ 创建SSH socket目录
)

:: 配置SSH
echo ℹ️  配置SSH连接...
set SSH_CONFIG=%USERPROFILE%\.ssh\config

:: 检查是否已有配置
findstr /C:"**************" "%SSH_CONFIG%" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  SSH配置已存在，将备份原配置
    copy "%SSH_CONFIG%" "%SSH_CONFIG%.backup.%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%" >nul
)

:: 添加SSH配置
echo ℹ️  添加高算平台SSH配置...
(
echo.
echo # ========================================
echo # ESM-2抗菌肽项目 - 高算平台配置
echo # 自动生成于 2025-01-27
echo # ========================================
echo.
echo # 高算平台主连接
echo Host hpc-main
echo     HostName **************
echo     User s20223050931
echo     Port 22
echo     ServerAliveInterval 30
echo     ServerAliveCountMax 5
echo     TCPKeepAlive yes
echo     Compression yes
echo     ControlMaster auto
echo     ControlPath ~/.ssh/sockets/%%r@%%h-%%p
echo     ControlPersist 600
echo     ClientAliveInterval 60
echo     ClientAliveCountMax 3
echo.
echo # 直接连接（备用）
echo Host hpc-direct
echo     HostName **************
echo     User s20223050931
echo     Port 22
echo     ServerAliveInterval 30
echo     ServerAliveCountMax 5
echo     Compression yes
echo.
echo # Jupyter端口转发配置
echo Host hpc-jupyter
echo     HostName **************
echo     User s20223050931
echo     Port 22
echo     LocalForward 8888 localhost:8888
echo     LocalForward 8889 localhost:8889
echo     LocalForward 8890 localhost:8890
echo     ServerAliveInterval 30
echo     ServerAliveCountMax 5
echo     Compression yes
echo.
) >> "%SSH_CONFIG%"

echo ✅ SSH配置添加完成

:: 测试SSH连接
echo ℹ️  测试SSH连接...
ssh -o ConnectTimeout=10 -o BatchMode=yes s20223050931@************** "echo SSH连接测试成功" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ SSH连接测试成功！
) else (
    echo ⚠️  SSH连接测试失败，可能需要手动输入密码或配置密钥
    echo    请尝试手动连接: ssh s20223050931@**************
)

:: 显示VSCode配置信息
echo ℹ️  VSCode配置信息...
echo 已创建的VSCode配置文件:
if exist ".vscode\settings.json" echo   ✅ .vscode\settings.json - 项目设置
if exist ".vscode\tasks.json" echo   ✅ .vscode\tasks.json - 任务配置
if exist ".vscode\launch.json" echo   ✅ .vscode\launch.json - 调试配置
if exist ".vscode\extensions.json" echo   ✅ .vscode\extensions.json - 扩展推荐

echo.
echo ==========================================
echo 🎉 VSCode高算平台配置完成！
echo ==========================================
echo.
echo 📋 下一步操作:
echo 1. 打开VSCode
echo 2. 安装推荐的扩展（VSCode会自动提示）
echo    - Remote - SSH
echo    - Python
echo    - Jupyter
echo 3. 按 Ctrl+Shift+P，输入 'Remote-SSH: Connect to Host'
echo 4. 选择 'hpc-main' 或输入 's20223050931@**************'
echo 5. 连接成功后，在VSCode终端中运行:
echo    ./scripts/vscode_hpc_integration.sh
echo.
echo 🔧 可用的VSCode任务 (Ctrl+Shift+P → Tasks: Run Task):
echo   - 🚀 启动高算平台Jupyter
echo   - 🔧 配置高算环境
echo   - 🧪 验证ESM兼容性
echo   - 📊 检查作业状态
echo   - 🧹 清理环境
echo.
echo 📖 详细指南: docs\vscode_hpc_guide.md
echo.
echo 💡 提示:
echo   - 首次连接可能需要输入密码
echo   - 建议配置SSH密钥以免密登录
echo   - 如遇问题，请查看 %USERPROFILE%\.ssh\config 文件
echo.
echo ✅ 配置完成！现在可以在VSCode中使用高算平台了！
echo.
pause
