#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 在高算平台上启动Jupyter Lab的SLURM作业脚本

#SBATCH --job-name=amp_jupyter          # 作业名称
#SBATCH --partition=cpu                 # 使用CPU分区（根据您的需求调整）
#SBATCH --cpus-per-task=8               # CPU核心数
#SBATCH --mem=32G                       # 内存
#SBATCH --time=08:00:00                 # 最大运行时间
#SBATCH --output=logs/jupyter_%j.out    # 标准输出文件
#SBATCH --error=logs/jupyter_%j.err     # 错误输出文件

# 创建日志目录
mkdir -p logs

# 打印作业信息
echo "=========================================="
echo "🚀 抗菌肽ESM-2项目 - Jupyter Lab启动"
echo "=========================================="
echo "作业ID: $SLURM_JOB_ID"
echo "节点名称: $SLURMD_NODENAME"
echo "开始时间: $(date)"
echo "CPU核心数: $SLURM_CPUS_PER_TASK"
echo "分配内存: 32GB"
echo "=========================================="

# 加载必要的模块（根据你的集群环境调整）
# 常见的模块加载命令，请根据您的高算平台调整
# module load anaconda3/2023.03
# module load python/3.9
# module load gcc/9.3.0

# 激活conda环境
echo "🔧 激活conda环境..."
source ~/miniconda3/etc/profile.d/conda.sh || source ~/anaconda3/etc/profile.d/conda.sh
conda activate amp_esm2

# 验证环境
echo "🔍 验证环境配置..."
echo "Python版本: $(python --version)"
echo "工作目录: $(pwd)"
echo "Conda环境: $CONDA_DEFAULT_ENV"

# 验证关键包
echo "📦 验证关键依赖包..."
python -c "
try:
    import torch
    print(f'✅ PyTorch版本: {torch.__version__}')
    print(f'✅ CUDA可用性: {torch.cuda.is_available()}')
    if torch.cuda.is_available():
        print(f'✅ GPU数量: {torch.cuda.device_count()}')
    else:
        print('ℹ️  使用CPU模式')
except ImportError as e:
    print(f'❌ PyTorch导入失败: {e}')

try:
    import pandas as pd
    print(f'✅ Pandas版本: {pd.__version__}')
except ImportError as e:
    print(f'❌ Pandas导入失败: {e}')

try:
    from Bio import SeqIO
    print('✅ BioPython导入成功')
except ImportError as e:
    print(f'❌ BioPython导入失败: {e}')

try:
    import fair_esm
    print('✅ ESM模型库导入成功')
except ImportError as e:
    print(f'❌ ESM模型库导入失败: {e}')
"

# 设置环境变量
export OMP_NUM_THREADS=$SLURM_CPUS_PER_TASK
export MKL_NUM_THREADS=$SLURM_CPUS_PER_TASK
export NUMEXPR_NUM_THREADS=$SLURM_CPUS_PER_TASK

# 获取节点信息
NODE_IP=$(hostname -i)
PORT=8888

# 检查端口是否被占用，如果被占用则使用其他端口
while netstat -an | grep -q ":$PORT "; do
    PORT=$((PORT + 1))
done

echo "=========================================="
echo "🚀 启动Jupyter Lab..."
echo "节点IP: $NODE_IP"
echo "端口: $PORT"
echo "项目目录: $(pwd)"
echo "=========================================="

# 生成Jupyter配置
echo "📝 生成Jupyter配置..."
jupyter lab --generate-config

# 启动Jupyter Lab
echo "🌟 正在启动Jupyter Lab服务..."
jupyter lab \
    --ip=0.0.0.0 \
    --port=$PORT \
    --no-browser \
    --allow-root \
    --notebook-dir=$(pwd) \
    --ServerApp.token='' \
    --ServerApp.password='' \
    --ServerApp.allow_origin='*' \
    --ServerApp.disable_check_xsrf=True \
    --ServerApp.allow_remote_access=True

echo "=========================================="
echo "Jupyter Lab已停止"
echo "结束时间: $(date)"
echo "=========================================="
