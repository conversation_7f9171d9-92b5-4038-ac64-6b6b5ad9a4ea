#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 在高算平台上启动Jupyter Lab的SLURM作业脚本

#SBATCH --job-name=amp_jupyter          # 作业名称
#SBATCH --partition=gpu                 # 分区名称（根据你的集群调整）
#SBATCH --gres=gpu:1                    # 申请1个GPU
#SBATCH --cpus-per-task=8               # CPU核心数
#SBATCH --mem=32G                       # 内存
#SBATCH --time=08:00:00                 # 最大运行时间
#SBATCH --output=logs/jupyter_%j.out    # 标准输出文件
#SBATCH --error=logs/jupyter_%j.err     # 错误输出文件

# 创建日志目录
mkdir -p logs

# 打印作业信息
echo "=========================================="
echo "作业ID: $SLURM_JOB_ID"
echo "节点名称: $SLURMD_NODENAME"
echo "开始时间: $(date)"
echo "=========================================="

# 加载必要的模块（根据你的集群环境调整）
# module load cuda/11.8
# module load anaconda3

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate amp_esm2

# 验证环境
echo "🔍 验证环境..."
echo "Python版本: $(python --version)"
echo "PyTorch版本: $(python -c 'import torch; print(torch.__version__)')"
echo "CUDA可用性: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "GPU数量: $(python -c 'import torch; print(torch.cuda.device_count())')"

# 设置环境变量
export CUDA_VISIBLE_DEVICES=$SLURM_LOCALID
export OMP_NUM_THREADS=$SLURM_CPUS_PER_TASK

# 获取节点IP和端口
NODE_IP=$(hostname -i)
PORT=8888

echo "=========================================="
echo "🚀 启动Jupyter Lab..."
echo "节点IP: $NODE_IP"
echo "端口: $PORT"
echo "=========================================="

# 启动Jupyter Lab
jupyter lab \
    --ip=0.0.0.0 \
    --port=$PORT \
    --no-browser \
    --allow-root \
    --notebook-dir=/path/to/your/project \
    --ServerApp.token='' \
    --ServerApp.password='' \
    --ServerApp.allow_origin='*' \
    --ServerApp.disable_check_xsrf=True

echo "=========================================="
echo "Jupyter Lab已停止"
echo "结束时间: $(date)"
echo "=========================================="
