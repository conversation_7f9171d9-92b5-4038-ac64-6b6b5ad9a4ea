# SSH配置文件模板
# 将此内容添加到您的 ~/.ssh/config 文件中

# 高算平台主连接
Host hpc-main
    HostName **************
    User s20223050931
    Port 22
    ServerAliveInterval 60
    ServerAliveCountMax 3
    TCPKeepAlive yes
    
    # 如果有密钥文件，取消注释下面一行
    # IdentityFile ~/.ssh/id_rsa
    
    # 压缩传输
    Compression yes
    
    # 连接复用
    ControlMaster auto
    ControlPath ~/.ssh/sockets/%r@%h-%p
    ControlPersist 600

# 计算节点连接（通过跳板机）
Host hpc-compute
    HostName %h
    User s20223050931
    ProxyJump hpc-main
    ServerAliveInterval 60
    ServerAliveCountMax 3
    
    # Jupyter端口转发
    LocalForward 8888 localhost:8888
    LocalForward 8889 localhost:8889
    LocalForward 8890 localhost:8890

# 使用说明:
# 1. 将此配置复制到 ~/.ssh/config
# 2. 创建socket目录: mkdir -p ~/.ssh/sockets
# 3. 连接主节点: ssh hpc-main
# 4. 在VSCode中使用Remote-SSH连接到 hpc-main
