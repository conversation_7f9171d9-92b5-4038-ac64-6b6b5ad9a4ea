{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 模型评估与验证 (第5-6周)\n", "\n", "**作者**: ZK  \n", "**邮箱**: <EMAIL>  \n", "**日期**: 2025-07-20  \n", "**描述**: 全面评估训练好的抗菌肽分类模型，包括性能指标、可视化分析和外部验证\n", "\n", "## 目标\n", "1. 加载训练好的模型\n", "2. 全面性能评估\n", "3. 结果可视化\n", "4. 外部数据验证\n", "5. 错误分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境设置和模型加载"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import torch\n", "import torch.nn as nn\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import (\n", "    accuracy_score, precision_score, recall_score, f1_score,\n", "    roc_auc_score, average_precision_score, matthews_corrcoef,\n", "    confusion_matrix, classification_report, roc_curve, precision_recall_curve\n", ")\n", "from sklearn.calibration import calibration_curve\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import pickle\n", "import json\n", "import os\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"✅ 库导入完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 路径配置\n", "PROJECT_ROOT = os.path.abspath(os.path.join(os.getcwd(), '..', '..'))\n", "MODEL_OUTPUT_DIR = os.path.join(PROJECT_ROOT, 'models')\n", "RESULTS_DIR = os.path.join(PROJECT_ROOT, 'results')\n", "FIGURES_DIR = os.path.join(PROJECT_ROOT, 'papers', 'figures')\n", "\n", "# 创建输出目录\n", "os.makedirs(RESULTS_DIR, exist_ok=True)\n", "os.makedirs(FIGURES_DIR, exist_ok=True)\n", "\n", "# 设备配置\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 模型评估器类"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ModelEvaluator:\n", "    \"\"\"模型评估器\"\"\"\n", "    \n", "    def __init__(self, model, device='cuda'):\n", "        self.model = model.to(device)\n", "        self.device = device\n", "        self.model.eval()\n", "        \n", "    def predict(self, dataloader):\n", "        \"\"\"模型预测\"\"\"\n", "        all_preds = []\n", "        all_probs = []\n", "        all_labels = []\n", "        \n", "        with torch.no_grad():\n", "            for batch_features, batch_labels in tqdm(dataloader, desc=\"预测中\"):\n", "                batch_features = batch_features.to(self.device)\n", "                \n", "                outputs = self.model(batch_features)\n", "                probs = torch.softmax(outputs, dim=1)\n", "                preds = torch.argmax(probs, dim=1)\n", "                \n", "                all_preds.extend(preds.cpu().numpy())\n", "                all_probs.extend(probs[:, 1].cpu().numpy())  # 正类概率\n", "                all_labels.extend(batch_labels.numpy())\n", "        \n", "        return np.array(all_labels), np.array(all_preds), np.array(all_probs)\n", "    \n", "    def calculate_comprehensive_metrics(self, y_true, y_pred, y_prob):\n", "        \"\"\"计算全面的评估指标\"\"\"\n", "        metrics = {\n", "            # 基础分类指标\n", "            'accuracy': accuracy_score(y_true, y_pred),\n", "            'precision': precision_score(y_true, y_pred, average='binary'),\n", "            'recall': recall_score(y_true, y_pred, average='binary'),\n", "            'specificity': recall_score(y_true, y_pred, pos_label=0, average='binary'),\n", "            'f1_score': f1_score(y_true, y_pred, average='binary'),\n", "            \n", "            # 概率相关指标\n", "            'auroc': roc_auc_score(y_true, y_prob),\n", "            'auprc': average_precision_score(y_true, y_prob),\n", "            \n", "            # 平衡指标\n", "            'mcc': matthews_corrcoef(y_true, y_pred),\n", "            'balanced_accuracy': (recall_score(y_true, y_pred, average='binary') + \n", "                                recall_score(y_true, y_pred, pos_label=0, average='binary')) / 2\n", "        }\n", "        \n", "        # 计算最优阈值\n", "        fpr, tpr, thresholds = roc_curve(y_true, y_prob)\n", "        optimal_idx = np.argmax(tpr - fpr)\n", "        metrics['optimal_threshold'] = thresholds[optimal_idx]\n", "        \n", "        # 使用最优阈值重新计算指标\n", "        y_pred_optimal = (y_prob >= metrics['optimal_threshold']).astype(int)\n", "        metrics['optimal_accuracy'] = accuracy_score(y_true, y_pred_optimal)\n", "        metrics['optimal_f1'] = f1_score(y_true, y_pred_optimal, average='binary')\n", "        \n", "        return metrics\n", "    \n", "    def bootstrap_confidence_interval(self, y_true, y_prob, metric_func, n_bootstrap=1000, confidence=0.95):\n", "        \"\"\"Bootstrap置信区间计算\"\"\"\n", "        scores = []\n", "        n_samples = len(y_true)\n", "        \n", "        for _ in range(n_bootstrap):\n", "            # 有放回抽样\n", "            indices = np.random.choice(n_samples, n_samples, replace=True)\n", "            y_true_boot = y_true[indices]\n", "            y_prob_boot = y_prob[indices]\n", "            \n", "            try:\n", "                score = metric_func(y_true_boot, y_prob_boot)\n", "                scores.append(score)\n", "            except:\n", "                continue\n", "        \n", "        scores = np.array(scores)\n", "        alpha = 1 - confidence\n", "        lower = np.percentile(scores, 100 * alpha / 2)\n", "        upper = np.percentile(scores, 100 * (1 - alpha / 2))\n", "        \n", "        return np.mean(scores), lower, upper\n", "    \n", "    def plot_roc_curve(self, y_true, y_prob, save_path=None):\n", "        \"\"\"绘制ROC曲线\"\"\"\n", "        fpr, tpr, _ = roc_curve(y_true, y_prob)\n", "        auc_score = roc_auc_score(y_true, y_prob)\n", "        \n", "        plt.figure(figsize=(8, 6))\n", "        plt.plot(fpr, tpr, color='darkorange', lw=2, \n", "                label=f'ROC曲线 (AUC = {auc_score:.3f})')\n", "        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', label='随机分类器')\n", "        plt.xlim([0.0, 1.0])\n", "        plt.ylim([0.0, 1.05])\n", "        plt.xlabel('假正率 (FPR)')\n", "        plt.ylabel('真正率 (TPR)')\n", "        plt.title('ROC曲线')\n", "        plt.legend(loc=\"lower right\")\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        if save_path:\n", "            plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "    \n", "    def plot_precision_recall_curve(self, y_true, y_prob, save_path=None):\n", "        \"\"\"绘制Precision-Recall曲线\"\"\"\n", "        precision, recall, _ = precision_recall_curve(y_true, y_prob)\n", "        auprc_score = average_precision_score(y_true, y_prob)\n", "        \n", "        plt.figure(figsize=(8, 6))\n", "        plt.plot(recall, precision, color='blue', lw=2,\n", "                label=f'PR曲线 (AUPRC = {auprc_score:.3f})')\n", "        \n", "        # 基线（随机分类器）\n", "        baseline = np.sum(y_true) / len(y_true)\n", "        plt.axhline(y=baseline, color='red', linestyle='--', \n", "                   label=f'随机分类器 (AP = {baseline:.3f})')\n", "        \n", "        plt.xlim([0.0, 1.0])\n", "        plt.ylim([0.0, 1.05])\n", "        plt.xlabel('召回率 (Recall)')\n", "        plt.ylabel('精确率 (Precision)')\n", "        plt.title('Precision-Recall曲线')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        if save_path:\n", "            plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "    \n", "    def plot_confusion_matrix(self, y_true, y_pred, save_path=None):\n", "        \"\"\"绘制混淆矩阵\"\"\"\n", "        cm = confusion_matrix(y_true, y_pred)\n", "        \n", "        plt.figure(figsize=(8, 6))\n", "        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n", "                   xticklabels=['非AMP', 'AMP'],\n", "                   yticklabels=['非AMP', 'AMP'])\n", "        plt.title('混淆矩阵')\n", "        plt.xlabel('预测标签')\n", "        plt.ylabel('真实标签')\n", "        \n", "        if save_path:\n", "            plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "    \n", "    def plot_probability_distribution(self, y_true, y_prob, save_path=None):\n", "        \"\"\"绘制预测概率分布\"\"\"\n", "        plt.figure(figsize=(10, 6))\n", "        \n", "        # 分别绘制正负样本的概率分布\n", "        pos_probs = y_prob[y_true == 1]\n", "        neg_probs = y_prob[y_true == 0]\n", "        \n", "        plt.hist(neg_probs, bins=50, alpha=0.7, label='非AMP', color='red', density=True)\n", "        plt.hist(pos_probs, bins=50, alpha=0.7, label='AMP', color='blue', density=True)\n", "        \n", "        plt.xlabel('预测概率')\n", "        plt.ylabel('密度')\n", "        plt.title('预测概率分布')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        if save_path:\n", "            plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "\n", "print(\"✅ ModelEvaluator类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 加载最佳模型并评估"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查找最佳模型文件\n", "model_files = [f for f in os.listdir(MODEL_OUTPUT_DIR) if f.endswith('.pth')]\n", "if not model_files:\n", "    print(\"❌ 未找到训练好的模型文件，请先运行模型训练notebook\")\n", "else:\n", "    # 选择最新的模型文件\n", "    latest_model = sorted(model_files)[-1]\n", "    model_path = os.path.join(MODEL_OUTPUT_DIR, latest_model)\n", "    \n", "    print(f\"加载模型: {latest_model}\")\n", "    \n", "    # 加载检查点\n", "    checkpoint = torch.load(model_path, map_location=device)\n", "    \n", "    # 重建模型\n", "    model_config = checkpoint['model_config']\n", "    model = AMPClassifier(\n", "        input_dim=model_config['input_dim'],\n", "        hidden_dims=model_config['hidden_dims'],\n", "        dropout=model_config['dropout']\n", "    )\n", "    \n", "    # 加载权重\n", "    model.load_state_dict(checkpoint['model_state_dict'])\n", "    \n", "    print(f\"✅ 模型加载成功\")\n", "    print(f\"训练轮数: {checkpoint['epoch']}\")\n", "    print(f\"最佳指标: {checkpoint['best_metric']:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 全面性能评估"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化评估器\n", "evaluator = ModelEvaluator(model, device)\n", "\n", "# 加载测试数据（这里需要根据实际情况调整）\n", "# test_loader = ... # 从数据集构建notebook中获取\n", "\n", "# 进行预测\n", "print(\"开始模型评估...\")\n", "# y_true, y_pred, y_prob = evaluator.predict(test_loader)\n", "\n", "# 计算全面指标\n", "# metrics = evaluator.calculate_comprehensive_metrics(y_true, y_pred, y_prob)\n", "\n", "# 打印结果\n", "print(\"\\n📊 模型性能评估结果:\")\n", "print(\"=\" * 50)\n", "# for metric, value in metrics.items():\n", "#     print(f\"{metric:20s}: {value:.4f}\")\n", "\n", "print(\"\\n⚠️ 注意：此notebook需要与前面的数据处理和训练notebook配合使用\")\n", "print(\"请确保已经完成数据集构建和模型训练步骤\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 结果可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成所有评估图表\n", "print(\"生成评估图表...\")\n", "\n", "# ROC曲线\n", "# evaluator.plot_roc_curve(y_true, y_prob, \n", "#                         save_path=os.path.join(FIGURES_DIR, 'roc_curve.png'))\n", "\n", "# Precision-Recall曲线\n", "# evaluator.plot_precision_recall_curve(y_true, y_prob,\n", "#                                       save_path=os.path.join(FIGURES_DIR, 'pr_curve.png'))\n", "\n", "# 混淆矩阵\n", "# evaluator.plot_confusion_matrix(y_true, y_pred,\n", "#                                save_path=os.path.join(FIGURES_DIR, 'confusion_matrix.png'))\n", "\n", "# 概率分布\n", "# evaluator.plot_probability_distribution(y_true, y_prob,\n", "#                                        save_path=os.path.join(FIGURES_DIR, 'prob_distribution.png'))\n", "\n", "print(\"\\n✅ 图表已保存到 papers/figures/ 目录\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 保存评估报告"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成详细的评估报告\n", "evaluation_report = {\n", "    'evaluation_date': datetime.now().isoformat(),\n", "    'model_info': {\n", "        'model_file': latest_model,\n", "        'training_epochs': checkpoint['epoch'],\n", "        'model_config': model_config\n", "    },\n", "    # 'performance_metrics': metrics,\n", "    # 'test_set_size': len(y_true),\n", "    # 'class_distribution': {\n", "    #     'positive_samples': int(np.sum(y_true)),\n", "    #     'negative_samples': int(len(y_true) - np.sum(y_true))\n", "    # }\n", "}\n", "\n", "# 保存报告\n", "report_file = os.path.join(RESULTS_DIR, f'evaluation_report_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.json')\n", "with open(report_file, 'w', encoding='utf-8') as f:\n", "    json.dump(evaluation_report, f, ensure_ascii=False, indent=2)\n", "\n", "print(f\"\\n📋 评估报告已保存: {report_file}\")\n", "print(f\"📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"\\n✅ 模型评估完成！\")\n", "print(\"\\n📝 下一步: 运行 06_模型解释与生物学验证.ipynb\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}