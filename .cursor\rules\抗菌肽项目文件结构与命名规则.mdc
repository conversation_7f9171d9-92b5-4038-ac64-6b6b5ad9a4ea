---
description:
globs:
alwaysApply: false
---
# 抗菌肽项目文件结构与命名规则 (v1.0)

> 本规则适用于“抗菌肽文章撰写”项目及其所有子任务，旨在统一文件夹结构、脚本命名方式，并规范记忆库（memory-bank）的使用。

---

## 1. 顶层目录结构

```
抗菌肽文章撰写/
├── data/                # 数据目录
│   ├── raw/             # 原始下载数据（只读）
│   ├── processed/       # 预处理、清洗后的中间数据
│   └── analysis_ready/  # 可直接用于模型或分析的最终数据
├── scripts/             # 所有 Python / R / Bash 脚本
│   ├── data_download/   # 数据下载与同步脚本
│   ├── data_prep/       # 数据清洗与预处理脚本
│   ├── data_analysis/   # 统计分析、可视化或机器学习脚本
│   └── utils/           # 通用工具函数脚本
├── papers/              # 学术论文、图表及其素材
│   ├── drafts/          # LaTeX/Markdown 初稿
│   ├── figures/         # 所有图表（SVG/PNG/PDF 等）
│   └── supplementary/   # 补充材料
├── memory-bank/         # 记忆库（遵循 RIPERT-8 指令自动管理）
│   ├── archive/         # 历史归档
│   ├── project_brief.md # 项目简介
│   ├── system_patterns.md
│   ├── tech_stack.md
│   ├── active_context.md
│   └── progress_log.md
└── README.md            # 项目总览说明
```

### 1.1 新建文件夹
当新增模块或实验时，**严禁**在顶层创建散乱目录，必须按上述分类放入对应子目录。若确需新增，请在 `memory-bank/progress_log.md` 中记录理由与日期。

---

## 2. 文件与脚本命名规范

### 2.1 数据文件
- 格式：`<数据来源>_<数据类型>_<YYYYMMDD>.<扩展名>`
  - 示例：`APD3_raw_20250720.csv`
- **数据来源**：官方数据库或文献简称（如 APD3、DBAASP）。
- **数据类型**：raw / cleaned / merged / stats 等。
- **日期**：数据生成或下载日期，8 位数字。

### 2.2 Python / R 脚本
- 均放置于 `scripts/` 及其子目录，禁止散落。
- 文件头部必须包含中文头注，格式遵循：
  ```python
  # 作者: ZK
  # 邮箱: <EMAIL>
  # 日期: 2025-07-20
  # 描述: [脚本功能简要说明]
  ```
- 文件名规则：`<动作>_<对象>[_<补充>].py`
  - **动作**：download / clean / merge / analyze / visualize / train / eval 等
  - **对象**：apd3 / dbaasp / peptide_stats 等
  - **补充**：可选，用于区分版本或详细场景
  - 示例：`download_apd3.py`, `analyze_antimicrobial_activity.py`

### 2.3 图表与论文草稿
- 图表命名：`fig<编号>_<关键词>.<svg|png|pdf>`，例如 `fig01_activity_distribution.svg`
- 论文草稿：`抗菌肽论文_draft_v<n>.md`，版本号递增。

---

## 3. 记忆库（memory-bank）使用规范

1. **信息记录**：
   - 每次新增数据文件、脚本或重要结果，必须在 `progress_log.md` 追加条目，内容包括：日期、文件相对路径、变更摘要、责任人。
2. **活跃上下文**：
   - 当前正在处理的数据集或论文章节，应在 `active_context.md` 中维护，保持最新。
3. **版本归档**：
   - 当任务主线改变或阶段性里程碑完成时，使用今天日期创建归档目录：`memory-bank/archive/20250720_阶段命名/`，并移动旧的 active_context。
4. **规范引用**：
   - 在规则或脚本内引用文件时，请使用 `[文件名](mdc:相对路径)` 格式，示例：`[download_apd3.py](mdc:scripts/data_download/download_apd3.py)`。

---

## 4. 开发流程约定

| 步骤 | 说明 | 产出物 |
|------|------|--------|
| A | 数据源确认 | `progress_log.md` 更新 |
| B | 数据下载 | `data/raw/` 新文件 + 下载脚本 |
| C | 数据清洗 | `data/processed/` 文件 + 清洗脚本 |
| D | 数据分析 | 统计图表、模型结果 |
| E | 论文撰写 | `papers/drafts/` 草稿 |
| F | 归档与记录 | `memory-bank/` 更新 |

执行顺序 A→F，任何偏离需在 `progress_log.md` 标注。

---

## 5. 规则更新

- 更新者须在顶部 `vX.Y` 记录版本号与日期，并在 `progress_log.md` 中新增“规则更新”条目。
- **禁止**随意修改历史内容，若需大幅调整，使用归档机制。

---

> **注**：本规则文件自身即为 `.cursor/rules` 规则体系的一部分，任何违反本规则的目录或命名将触发 CI 检查失败。

