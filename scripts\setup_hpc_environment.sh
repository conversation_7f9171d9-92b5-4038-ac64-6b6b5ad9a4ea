#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 高算平台环境配置脚本 - 专门为ESM-2抗菌肽项目设计

set -e  # 遇到错误立即退出

echo "🚀 开始配置高算平台环境..."
echo "项目: ESM-2抗菌肽分类"
echo "时间: $(date)"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色信息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查必要的命令
check_command() {
    if command -v $1 &> /dev/null; then
        print_success "$1 已安装"
        return 0
    else
        print_error "$1 未找到"
        return 1
    fi
}

# 1. 检查基础环境
print_info "检查基础环境..."
check_command "conda" || {
    print_error "Conda未安装，请先安装Miniconda或Anaconda"
    exit 1
}

# 2. 创建项目目录结构
print_info "创建项目目录结构..."
mkdir -p logs
mkdir -p data/{raw,processed,analysis_ready}
mkdir -p models
mkdir -p results/{figures,tables,checkpoints}
mkdir -p temp
print_success "目录结构创建完成"

# 3. 设置环境变量
ENV_NAME="amp_esm2"
PYTHON_VERSION="3.9"

print_info "环境配置参数:"
echo "  - 环境名称: $ENV_NAME"
echo "  - Python版本: $PYTHON_VERSION"
echo "  - 工作目录: $(pwd)"

# 4. 创建conda环境
print_info "创建conda环境: $ENV_NAME"
if conda env list | grep -q "^$ENV_NAME "; then
    print_warning "环境 $ENV_NAME 已存在，将删除并重新创建"
    conda env remove -n $ENV_NAME -y
fi

conda create -n $ENV_NAME python=$PYTHON_VERSION -y
print_success "Conda环境创建完成"

# 5. 激活环境
print_info "激活环境..."
eval "$(conda shell.bash hook)"
conda activate $ENV_NAME

# 验证环境激活
if [[ "$CONDA_DEFAULT_ENV" == "$ENV_NAME" ]]; then
    print_success "环境激活成功: $CONDA_DEFAULT_ENV"
else
    print_error "环境激活失败"
    exit 1
fi

# 6. 更新pip
print_info "更新pip..."
pip install --upgrade pip
print_success "pip更新完成"

# 7. 安装PyTorch (CPU版本，适合大多数高算平台)
print_info "安装PyTorch..."
if command -v nvidia-smi &> /dev/null; then
    print_info "检测到NVIDIA GPU，安装CUDA版本"
    # 根据CUDA版本选择合适的PyTorch
    conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
else
    print_info "未检测到GPU，安装CPU版本"
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
fi
print_success "PyTorch安装完成"

# 8. 安装核心依赖
print_info "安装核心依赖包..."

# 生物信息学工具
pip install biopython>=1.81

# 数据处理
pip install pandas>=1.5.0 numpy>=1.24.0 scikit-learn>=1.3.0

# ESM模型
pip install fair-esm>=2.0.0 transformers>=4.21.0

# 可视化
pip install matplotlib>=3.6.0 seaborn>=0.12.0 plotly>=5.0.0

# 工具包
pip install tqdm>=4.64.0 requests>=2.28.0

# Jupyter相关
pip install jupyter jupyterlab ipywidgets

# 其他有用的包
pip install umap-learn>=0.5.0 optuna>=3.0.0

print_success "核心依赖安装完成"

# 9. 验证安装
print_info "验证安装..."
python -c "
import sys
print(f'Python版本: {sys.version}')

# 验证核心包
packages = {
    'torch': 'PyTorch',
    'pandas': 'Pandas', 
    'numpy': 'NumPy',
    'Bio': 'BioPython',
    'sklearn': 'Scikit-learn',
    'matplotlib': 'Matplotlib',
    'seaborn': 'Seaborn',
    'tqdm': 'TQDM'
}

failed_packages = []
for package, name in packages.items():
    try:
        __import__(package)
        print(f'✅ {name}: 导入成功')
    except ImportError as e:
        print(f'❌ {name}: 导入失败 - {e}')
        failed_packages.append(name)

# 验证PyTorch
try:
    import torch
    print(f'PyTorch版本: {torch.__version__}')
    print(f'CUDA可用: {torch.cuda.is_available()}')
    if torch.cuda.is_available():
        print(f'CUDA版本: {torch.version.cuda}')
        print(f'GPU数量: {torch.cuda.device_count()}')
except Exception as e:
    print(f'PyTorch验证失败: {e}')
    failed_packages.append('PyTorch')

# 验证ESM
try:
    import esm
    print('✅ ESM模型库: 导入成功')
except ImportError:
    try:
        import fair_esm
        print('✅ Fair-ESM模型库: 导入成功')
    except ImportError as e:
        print(f'❌ ESM模型库: 导入失败 - {e}')
        failed_packages.append('ESM')

if failed_packages:
    print(f'\\n⚠️  以下包安装可能有问题: {failed_packages}')
    sys.exit(1)
else:
    print('\\n🎉 所有核心包验证通过!')
"

if [ $? -eq 0 ]; then
    print_success "环境验证通过"
else
    print_error "环境验证失败，请检查错误信息"
    exit 1
fi

# 10. 创建环境激活脚本
print_info "创建环境激活脚本..."
cat > activate_env.sh << 'EOF'
#!/bin/bash
# 激活抗菌肽项目环境
source ~/miniconda3/etc/profile.d/conda.sh || source ~/anaconda3/etc/profile.d/conda.sh
conda activate amp_esm2
echo "🧬 抗菌肽ESM-2环境已激活"
echo "Python: $(python --version)"
echo "工作目录: $(pwd)"
EOF

chmod +x activate_env.sh
print_success "环境激活脚本创建完成: activate_env.sh"

# 11. 创建快速测试脚本
print_info "创建快速测试脚本..."
cat > test_environment.py << 'EOF'
#!/usr/bin/env python3
"""
快速环境测试脚本
测试ESM-2抗菌肽项目的关键功能
"""

def test_imports():
    """测试关键包导入"""
    print("🧪 测试包导入...")
    
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__}")
        
        import pandas as pd
        print(f"✅ Pandas {pd.__version__}")
        
        import numpy as np
        print(f"✅ NumPy {np.__version__}")
        
        from Bio import SeqIO
        print("✅ BioPython")
        
        import sklearn
        print(f"✅ Scikit-learn {sklearn.__version__}")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_esm_model():
    """测试ESM模型加载"""
    print("\n🧬 测试ESM模型...")
    
    try:
        import esm
        model, alphabet = esm.pretrained.esm2_t6_8M_UR50D()
        print("✅ ESM-2模型加载成功")
        return True
    except Exception as e:
        print(f"❌ ESM模型测试失败: {e}")
        return False

def test_basic_functionality():
    """测试基础功能"""
    print("\n⚙️  测试基础功能...")
    
    try:
        import torch
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        sequences = ["MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"]
        labels = [1]
        
        df = pd.DataFrame({'sequence': sequences, 'label': labels})
        print(f"✅ 数据框创建成功: {df.shape}")
        
        # 测试张量操作
        tensor = torch.randn(10, 5)
        print(f"✅ 张量操作成功: {tensor.shape}")
        
        return True
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始环境测试...")
    print("=" * 50)
    
    success = True
    success &= test_imports()
    success &= test_esm_model()
    success &= test_basic_functionality()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！环境配置成功！")
    else:
        print("❌ 部分测试失败，请检查环境配置")
EOF

print_success "测试脚本创建完成: test_environment.py"

# 12. 输出使用说明
echo ""
echo "=========================================="
print_success "🎉 高算平台环境配置完成！"
echo "=========================================="
echo ""
echo "📋 使用说明:"
echo "1. 激活环境:"
echo "   source activate_env.sh"
echo ""
echo "2. 测试环境:"
echo "   python test_environment.py"
echo ""
echo "3. 启动Jupyter Lab:"
echo "   sbatch scripts/run_jupyter_hpc.sh"
echo ""
echo "4. 运行项目:"
echo "   jupyter lab ESM2_AMP_Training_Complete.ipynb"
echo ""
echo "📁 重要目录:"
echo "  - 数据目录: data/"
echo "  - 模型目录: models/"
echo "  - 结果目录: results/"
echo "  - 日志目录: logs/"
echo ""
echo "💡 提示:"
echo "  - 使用 'conda activate amp_esm2' 激活环境"
echo "  - 使用 'conda deactivate' 退出环境"
echo "  - 查看作业状态: squeue -u $USER"
echo ""
print_success "配置完成！祝您实验顺利！🧬"
