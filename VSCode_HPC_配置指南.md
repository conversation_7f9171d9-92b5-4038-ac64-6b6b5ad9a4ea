# 🖥️ VSCode高算平台配置指南

## 🎯 目标
为您的高算平台 `s20223050931@**************` 配置VSCode集成环境，实现一键申请CPU节点并运行Jupyter。

## 🚀 一键配置（推荐）

### Windows用户
```cmd
# 双击运行或在命令行执行
configure_vscode_hpc.bat
```

### Linux/Mac用户
```bash
# 给脚本添加执行权限并运行
chmod +x configure_vscode_hpc.sh
./configure_vscode_hpc.sh
```

## 📋 手动配置步骤

### 第一步：安装VSCode扩展
在VSCode中安装以下扩展：
- **Remote - SSH** (必需)
- **Python** (必需)
- **Jupyter** (必需)
- **Pylance** (推荐)
- **Black Formatter** (推荐)

### 第二步：配置SSH连接

#### Windows用户
1. 打开 `%USERPROFILE%\.ssh\config` 文件（如果不存在则创建）
2. 添加以下配置：

```ssh
# 高算平台主连接
Host hpc-main
    HostName **************
    User s20223050931
    Port 22
    ServerAliveInterval 30
    ServerAliveCountMax 5
    TCPKeepAlive yes
    Compression yes

# Jupyter端口转发配置
Host hpc-jupyter
    HostName **************
    User s20223050931
    Port 22
    LocalForward 8888 localhost:8888
    LocalForward 8889 localhost:8889
    LocalForward 8890 localhost:8890
    ServerAliveInterval 30
    ServerAliveCountMax 5
    Compression yes
```

#### Linux/Mac用户
1. 打开 `~/.ssh/config` 文件
2. 添加相同的配置内容
3. 创建socket目录：`mkdir -p ~/.ssh/sockets`
4. 设置权限：`chmod 700 ~/.ssh && chmod 600 ~/.ssh/config`

### 第三步：测试连接
```bash
# 测试基本连接
ssh s20223050931@**************

# 测试别名连接
ssh hpc-main
```

### 第四步：VSCode连接
1. 在VSCode中按 `Ctrl+Shift+P`
2. 输入 `Remote-SSH: Connect to Host`
3. 选择 `hpc-main` 或输入 `s20223050931@**************`
4. 输入密码（首次连接）

## 🔧 项目配置文件说明

我已经为您创建了完整的VSCode配置：

### `.vscode/settings.json`
- Python解释器自动设置为conda环境
- Jupyter服务器配置
- 远程连接优化设置

### `.vscode/tasks.json`
提供以下快捷任务：
- **🚀 启动高算平台Jupyter** - 一键申请节点并启动Jupyter
- **🔧 配置高算环境** - 配置conda环境
- **🧪 验证ESM兼容性** - 测试ESM模型
- **📊 检查作业状态** - 查看SLURM作业
- **🧹 清理环境** - 清理临时文件

### `.vscode/launch.json`
调试配置：
- ESM-2训练调试
- 数据处理调试
- 兼容性测试调试

### `.vscode/extensions.json`
推荐扩展列表，VSCode会自动提示安装

## 🚀 使用流程

### 日常使用步骤：
1. **打开VSCode**
2. **连接高算平台**：
   - 按 `Ctrl+Shift+P`
   - 输入 `Remote-SSH: Connect to Host`
   - 选择 `hpc-main`
3. **申请CPU节点**：
   - 在VSCode终端运行：`./scripts/vscode_hpc_integration.sh`
   - 或使用任务：`Ctrl+Shift+P` → `Tasks: Run Task` → `🚀 启动高算平台Jupyter`
4. **连接Jupyter**：
   - 脚本会显示连接地址，如：`http://*************:8888`
   - 在VSCode中：`Ctrl+Shift+P` → `Jupyter: Specify Jupyter Server URI`
   - 输入显示的地址
5. **开始工作**：
   - 打开 `ESM2_AMP_Training_Complete.ipynb`
   - 先运行 `hpc_notebook_setup.ipynb` 进行环境配置
   - 开始您的ESM-2训练

## 🛠️ 常见问题解决

### 1. SSH连接失败
```bash
# 检查网络连通性
ping **************

# 检查SSH配置
cat ~/.ssh/config

# 手动测试连接
ssh -v s20223050931@**************
```

### 2. VSCode无法连接远程服务器
- 确保SSH配置正确
- 检查防火墙设置
- 尝试重启VSCode
- 清除VSCode远程连接缓存

### 3. Jupyter连接问题
- 检查节点IP地址是否正确
- 确认端口没有被占用
- 尝试使用SSH隧道方式

### 4. 环境激活失败
```bash
# 手动激活环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate amp_esm2

# 验证环境
which python
python -c "import torch; print(torch.__version__)"
```

## 📊 资源配置建议

### 默认CPU节点配置
- **CPU核心**: 16核
- **内存**: 64GB
- **运行时间**: 8小时
- **分区**: cpu

### 根据需求调整
在 `scripts/vscode_hpc_integration.sh` 中修改：
```bash
# 轻量测试
srun --partition=cpu --cpus-per-task=8 --mem=32G --time=04:00:00

# 正式训练
srun --partition=cpu --cpus-per-task=32 --mem=128G --time=24:00:00
```

## 🎯 最佳实践

1. **首次使用**：
   - 运行配置脚本进行自动配置
   - 测试SSH连接
   - 验证VSCode扩展安装

2. **日常使用**：
   - 使用VSCode任务快速启动
   - 定期保存检查点
   - 监控资源使用情况

3. **安全建议**：
   - 配置SSH密钥免密登录
   - 定期更新密码
   - 不要在公共网络使用

## 📞 技术支持

### 配置问题
- 运行测试脚本：`./test_hpc_connection.sh`
- 查看配置文件：`~/.ssh/config`
- 检查VSCode设置：`.vscode/settings.json`

### 连接问题
- 邮箱：<EMAIL>
- 文档：`docs/vscode_hpc_guide.md`
- 高算平台：s20223050931@**************

## 🎉 配置完成检查清单

- [ ] VSCode扩展已安装（Remote-SSH, Python, Jupyter）
- [ ] SSH配置已添加到 `~/.ssh/config`
- [ ] SSH连接测试成功
- [ ] VSCode可以连接到高算平台
- [ ] 项目脚本有执行权限
- [ ] 可以在VSCode中运行任务

完成以上检查后，您就可以在VSCode中愉快地使用高算平台进行ESM-2抗菌肽研究了！

---

**作者**: ZK  
**邮箱**: <EMAIL>  
**平台**: s20223050931@**************  
**日期**: 2025-01-27
