{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚀 高算平台环境配置单元\n", "\n", "**请在运行ESM2_AMP_Training_Complete.ipynb之前先运行此配置单元**\n", "\n", "此单元专门为高算平台优化，包含：\n", "- 环境检测和配置\n", "- 内存监控和优化\n", "- PyTorch性能调优\n", "- 检查点管理\n", "- 批处理大小自动调整"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python3\n", "\"\"\"\n", "高算平台Jupyter环境配置\n", "作者: ZK\n", "邮箱: <EMAIL>\n", "日期: 2025-01-27\n", "\"\"\"\n", "\n", "import os\n", "import sys\n", "import torch\n", "import gc\n", "import psutil\n", "import warnings\n", "from pathlib import Path\n", "import time\n", "\n", "# 忽略警告\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"🚀 初始化高算平台Jupyter环境...\")\n", "print(\"=\" * 60)\n", "\n", "# 1. 环境信息\n", "print(\"📊 环境信息:\")\n", "print(f\"  - Python版本: {sys.version}\")\n", "print(f\"  - 工作目录: {os.getcwd()}\")\n", "print(f\"  - CPU核心数: {os.cpu_count()}\")\n", "\n", "# 获取SLURM环境变量\n", "slurm_cpus = os.environ.get('SLURM_CPUS_PER_TASK', str(os.cpu_count()))\n", "slurm_mem = os.environ.get('SLURM_MEM_PER_NODE', 'Unknown')\n", "slurm_job_id = os.environ.get('SLURM_JOB_ID', 'Interactive')\n", "\n", "print(f\"  - SLURM作业ID: {slurm_job_id}\")\n", "print(f\"  - 分配CPU数: {slurm_cpus}\")\n", "print(f\"  - 分配内存: {slurm_mem}\")\n", "\n", "# 2. <PERSON><PERSON><PERSON><PERSON><PERSON>配置\n", "print(\"\\n🔧 PyTorch配置:\")\n", "print(f\"  - PyTorch版本: {torch.__version__}\")\n", "print(f\"  - CUDA可用: {torch.cuda.is_available()}\")\n", "\n", "# 设置CPU线程数\n", "cpu_threads = int(slurm_cpus)\n", "torch.set_num_threads(cpu_threads)\n", "print(f\"  - 设置CPU线程数: {cpu_threads}\")\n", "\n", "# 设置环境变量\n", "os.environ['OMP_NUM_THREADS'] = str(cpu_threads)\n", "os.environ['MKL_NUM_THREADS'] = str(cpu_threads)\n", "os.environ['NUMEXPR_NUM_THREADS'] = str(cpu_threads)\n", "os.environ['TOKENIZERS_PARALLELISM'] = 'false'\n", "\n", "if torch.cuda.is_available():\n", "    print(f\"  - GPU数量: {torch.cuda.device_count()}\")\n", "    for i in range(torch.cuda.device_count()):\n", "        print(f\"  - GPU {i}: {torch.cuda.get_device_name(i)}\")\n", "else:\n", "    print(\"  - 使用CPU模式\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. 内存监控和管理函数\n", "def monitor_memory():\n", "    \"\"\"监控内存使用情况\"\"\"\n", "    memory = psutil.virtual_memory()\n", "    print(f\"💾 内存状态:\")\n", "    print(f\"  - 总内存: {memory.total / (1024**3):.1f} GB\")\n", "    print(f\"  - 已使用: {memory.used / (1024**3):.1f} GB ({memory.percent:.1f}%)\")\n", "    print(f\"  - 可用内存: {memory.available / (1024**3):.1f} GB\")\n", "    \n", "    if memory.percent > 85:\n", "        print(\"⚠️  内存使用率较高，建议清理内存\")\n", "    \n", "    return memory.available / (1024**3)  # 返回可用内存GB数\n", "\n", "def clear_memory():\n", "    \"\"\"清理内存\"\"\"\n", "    gc.collect()\n", "    if torch.cuda.is_available():\n", "        torch.cuda.empty_cache()\n", "    print(\"🧹 内存清理完成\")\n", "\n", "def get_optimal_batch_size():\n", "    \"\"\"根据可用内存计算最优批处理大小\"\"\"\n", "    available_memory = monitor_memory()\n", "    \n", "    if available_memory > 50:\n", "        batch_size = 32\n", "    elif available_memory > 30:\n", "        batch_size = 16\n", "    elif available_memory > 20:\n", "        batch_size = 8\n", "    elif available_memory > 10:\n", "        batch_size = 4\n", "    else:\n", "        batch_size = 2\n", "    \n", "    print(f\"🎯 推荐批处理大小: {batch_size}\")\n", "    return batch_size\n", "\n", "# 执行初始内存检查\n", "initial_memory = monitor_memory()\n", "optimal_batch_size = get_optimal_batch_size()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. 目录结构创建\n", "def setup_directories():\n", "    \"\"\"创建项目目录结构\"\"\"\n", "    directories = [\n", "        'data/raw',\n", "        'data/processed', \n", "        'data/analysis_ready',\n", "        'models',\n", "        'results/figures',\n", "        'results/tables',\n", "        'results/checkpoints',\n", "        'logs',\n", "        'temp'\n", "    ]\n", "    \n", "    for dir_path in directories:\n", "        Path(dir_path).mkdir(parents=True, exist_ok=True)\n", "    \n", "    print(\"📁 目录结构创建完成\")\n", "\n", "setup_directories()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 5. ESM模型选择和检查点管理\n", "def select_esm_model():\n", "    \"\"\"根据可用资源选择合适的ESM模型\"\"\"\n", "    available_memory = psutil.virtual_memory().available / (1024**3)\n", "    \n", "    if available_memory > 40:\n", "        model_name = 'esm2_t33_650M_UR50D'\n", "        print(\"🧠 推荐使用: ESM-2 650M模型（大模型，高精度）\")\n", "    elif available_memory > 20:\n", "        model_name = 'esm2_t12_35M_UR50D'\n", "        print(\"🧠 推荐使用: ESM-2 35M模型（中等模型）\")\n", "    else:\n", "        model_name = 'esm2_t6_8M_UR50D'\n", "        print(\"🧠 推荐使用: ESM-2 8M模型（小模型，适合CPU）\")\n", "    \n", "    return model_name\n", "\n", "class CheckpointManager:\n", "    \"\"\"检查点管理器\"\"\"\n", "    \n", "    def __init__(self, checkpoint_dir=\"results/checkpoints\"):\n", "        self.checkpoint_dir = Path(checkpoint_dir)\n", "        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    def save_checkpoint(self, model, optimizer, epoch, loss, metrics=None, filename=None):\n", "        \"\"\"保存检查点\"\"\"\n", "        if filename is None:\n", "            filename = f\"checkpoint_epoch_{epoch}.pth\"\n", "        \n", "        checkpoint = {\n", "            'epoch': epoch,\n", "            'model_state_dict': model.state_dict(),\n", "            'optimizer_state_dict': optimizer.state_dict(),\n", "            'loss': loss,\n", "            'metrics': metrics or {},\n", "            'timestamp': time.time()\n", "        }\n", "        \n", "        filepath = self.checkpoint_dir / filename\n", "        torch.save(checkpoint, filepath)\n", "        print(f\"✅ 检查点已保存: {filename}\")\n", "        return filepath\n", "    \n", "    def load_checkpoint(self, model, optimizer, filename):\n", "        \"\"\"加载检查点\"\"\"\n", "        filepath = self.checkpoint_dir / filename\n", "        if not filepath.exists():\n", "            raise FileNotFoundError(f\"检查点文件不存在: {filename}\")\n", "        \n", "        checkpoint = torch.load(filepath, map_location='cpu')\n", "        model.load_state_dict(checkpoint['model_state_dict'])\n", "        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])\n", "        \n", "        print(f\"✅ 检查点已加载: {filename}\")\n", "        return checkpoint['epoch'], checkpoint['loss'], checkpoint.get('metrics', {})\n", "    \n", "    def list_checkpoints(self):\n", "        \"\"\"列出所有检查点\"\"\"\n", "        checkpoints = list(self.checkpoint_dir.glob(\"checkpoint_epoch_*.pth\"))\n", "        checkpoints.sort(key=lambda x: x.stat().st_mtime)\n", "        return checkpoints\n", "    \n", "    def get_latest_checkpoint(self):\n", "        \"\"\"获取最新检查点\"\"\"\n", "        checkpoints = self.list_checkpoints()\n", "        return checkpoints[-1] if checkpoints else None\n", "\n", "# 创建全局对象\n", "recommended_model = select_esm_model()\n", "checkpoint_manager = CheckpointManager()\n", "\n", "print(f\"📦 推荐ESM模型: {recommended_model}\")\n", "print(\"💾 检查点管理器已初始化\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 6. 训练进度监控和实用函数\n", "from tqdm.notebook import tqdm\n", "import matplotlib.pyplot as plt\n", "\n", "# 设置matplotlib后端\n", "%matplotlib inline\n", "plt.style.use('default')\n", "\n", "def create_progress_callback():\n", "    \"\"\"创建训练进度回调函数\"\"\"\n", "    def progress_callback(epoch, loss, metrics=None):\n", "        print(f\"📊 Epoch {epoch}: Loss = {loss:.4f}\")\n", "        if metrics:\n", "            for key, value in metrics.items():\n", "                print(f\"    {key}: {value:.4f}\")\n", "        \n", "        # 内存监控\n", "        if epoch % 5 == 0:\n", "            monitor_memory()\n", "            clear_memory()\n", "    \n", "    return progress_callback\n", "\n", "def plot_realtime_loss(losses, title=\"训练损失\"):\n", "    \"\"\"实时绘制损失曲线\"\"\"\n", "    plt.figure(figsize=(10, 4))\n", "    plt.plot(losses, 'b-', linewidth=2)\n", "    plt.title(title)\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid(True, alpha=0.3)\n", "    plt.show()\n", "\n", "# 创建进度回调\n", "progress_callback = create_progress_callback()\n", "\n", "print(\"📈 训练监控函数已创建\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 7. 最终配置总结\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"✅ 高算平台环境初始化完成!\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n📋 配置总结:\")\n", "print(f\"  - 推荐批处理大小: {optimal_batch_size}\")\n", "print(f\"  - 推荐ESM模型: {recommended_model}\")\n", "print(f\"  - CPU线程数: {cpu_threads}\")\n", "print(f\"  - 可用内存: {initial_memory:.1f} GB\")\n", "\n", "print(\"\\n🔧 可用函数:\")\n", "print(\"  - monitor_memory(): 监控内存使用\")\n", "print(\"  - clear_memory(): 清理内存\")\n", "print(\"  - checkpoint_manager.save_checkpoint(): 保存检查点\")\n", "print(\"  - checkpoint_manager.load_checkpoint(): 加载检查点\")\n", "print(\"  - plot_realtime_loss(): 实时绘制损失曲线\")\n", "\n", "print(\"\\n💡 使用建议:\")\n", "print(\"  1. 在训练开始前调用 clear_memory()\")\n", "print(\"  2. 每5-10个epoch保存一次检查点\")\n", "print(\"  3. 如果内存不足，减小批处理大小\")\n", "print(\"  4. 定期调用 monitor_memory() 检查资源使用\")\n", "\n", "print(\"\\n🚀 现在可以开始运行您的ESM-2训练代码了!\")\n", "print(\"请继续运行 ESM2_AMP_Training_Complete.ipynb 的其他单元\")\n", "\n", "# 导出重要变量供后续使用\n", "globals().update({\n", "    'OPTIMAL_BATCH_SIZE': optimal_batch_size,\n", "    'RECOMMENDED_MODEL': recommended_model,\n", "    'CPU_THREADS': cpu_threads,\n", "    'checkpoint_manager': checkpoint_manager,\n", "    'progress_callback': progress_callback\n", "})\n", "\n", "print(\"\\n✨ 全局变量已设置，可在后续单元中直接使用\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}