# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-20
# 描述: 全面的抗菌肽数据下载器 - 基于研究分析的改进版本

import os
import requests
import pandas as pd
import numpy as np
from Bio import SeqIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord
from datetime import datetime
import hashlib
from tqdm import tqdm
import time
import json
import re
from urllib.parse import urljoin, urlparse
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveAMPDownloader:
    """全面的抗菌肽数据下载器 - 覆盖更多数据库"""
    
    def __init__(self, output_dir, max_retries=3, timeout=60):
        self.output_dir = output_dir
        self.max_retries = max_retries
        self.timeout = timeout
        self.download_log = []
        
        # 全面的数据库配置 - 基于研究分析
        self.database_config = {
            # 核心数据库 - 已验证可用
            'dbAMP_3.0_antimicrobial': {
                'url': 'https://awi.cuhk.edu.cn/dbAMP/download/3.0/activity/dbAMP_Antimicrobial.fasta',
                'description': 'dbAMP 3.0 抗菌活性肽数据库 (14,050条记录)',
                'priority': 'high',
                'expected_format': 'fasta'
            },
            'dbAMP_3.0_antibacterial': {
                'url': 'https://awi.cuhk.edu.cn/dbAMP/download/3.0/activity/dbAMP_Antibacterial.fasta',
                'description': 'dbAMP 3.0 抗细菌肽数据库',
                'priority': 'high',
                'expected_format': 'fasta'
            },
            'DRAMP_general': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/general_amps.fasta',
                'description': 'DRAMP 4.0 通用抗菌肽数据库 (11,612条记录)',
                'priority': 'high',
                'expected_format': 'fasta'
            },
            'DRAMP_antimicrobial': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/Antimicrobial_amps.fasta',
                'description': 'DRAMP 4.0 抗菌活性肽数据库 (5,626条记录)',
                'priority': 'high',
                'expected_format': 'fasta'
            },
            'DRAMP_antibacterial': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/Antibacterial_amps.fasta',
                'description': 'DRAMP 4.0 抗细菌肽数据库 (4,159条记录)',
                'priority': 'high',
                'expected_format': 'fasta'
            },
            'DRAMP_natural': {
                'url': 'http://dramp.cpu-bioinfor.org/downloads/download.php?filename=download_data/DRAMP3.0_new/natural_amps.fasta',
                'description': 'DRAMP 4.0 天然抗菌肽数据库 (5,774条记录)',
                'priority': 'high',
                'expected_format': 'fasta'
            },
            
            # 补充数据库 - 需要特殊处理
            'APD_placeholder': {
                'url': 'https://aps.unmc.edu/downloads',
                'description': 'APD (Antimicrobial Peptide Database) - 需要手动获取',
                'priority': 'medium',
                'expected_format': 'manual',
                'note': '包含5,099个肽段，需要注册或联系管理员获取'
            },
            'CAMPR4_placeholder': {
                'url': 'https://camp.bicnirrh.res.in/',
                'description': 'CAMPR4 - 需要通过网页界面导出',
                'priority': 'medium',
                'expected_format': 'manual',
                'note': '包含24,243个序列（11,827天然+12,416合成）'
            },
            
            # 其他补充数据库
            'DBAASP_placeholder': {
                'url': 'https://dbaasp.org/api',
                'description': 'DBAASP - 通过API获取',
                'priority': 'low',
                'expected_format': 'api',
                'note': '需要使用REST API获取数据'
            }
        }
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
    def calculate_file_hash(self, filepath):
        """计算文件的SHA256哈希值"""
        hash_sha256 = hashlib.sha256()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def validate_fasta_content(self, filepath):
        """验证FASTA文件内容"""
        try:
            records = list(SeqIO.parse(filepath, "fasta"))
            if len(records) == 0:
                return False, "FASTA文件中没有有效序列"
            
            # 检查序列质量
            valid_sequences = 0
            for record in records:
                seq_str = str(record.seq).upper()
                # 检查是否包含有效的氨基酸字符
                if re.match(r'^[ACDEFGHIKLMNPQRSTVWYXBZ]+$', seq_str):
                    valid_sequences += 1
            
            if valid_sequences == 0:
                return False, "没有找到有效的蛋白质序列"
            
            return True, f"包含 {len(records)} 个序列，其中 {valid_sequences} 个有效"
            
        except Exception as e:
            return False, f"FASTA格式验证失败: {e}"
    
    def download_file(self, url, filename, description=""):
        """下载单个文件，包含改进的错误处理"""
        filepath = os.path.join(self.output_dir, filename)
        
        for attempt in range(self.max_retries):
            try:
                print(f"正在下载 {description} (尝试 {attempt + 1}/{self.max_retries})...")
                print(f"URL: {url}")
                
                # 设置请求头
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
                
                response = requests.get(url, timeout=self.timeout, stream=True, headers=headers)
                response.raise_for_status()
                
                # 检查响应内容
                content_type = response.headers.get('content-type', '').lower()
                print(f"响应类型: {content_type}")
                
                # 检查是否是HTML页面（可能是错误页面）
                if 'text/html' in content_type:
                    # 读取少量内容检查
                    sample_content = response.content[:1000].decode('utf-8', errors='ignore')
                    if '<html' in sample_content.lower() or '<!doctype' in sample_content.lower():
                        print("⚠️ 响应似乎是HTML页面，可能不是数据文件")
                
                # 获取文件大小
                total_size = int(response.headers.get('content-length', 0))
                
                with open(filepath, 'wb') as f, tqdm(
                    desc=filename,
                    total=total_size,
                    unit='B',
                    unit_scale=True,
                    unit_divisor=1024,
                ) as pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            pbar.update(len(chunk))
                
                # 验证文件
                file_size = os.path.getsize(filepath)
                if file_size == 0:
                    raise ValueError("下载的文件为空")
                
                # 验证FASTA格式
                if filename.endswith('.fasta') or filename.endswith('.fa'):
                    is_valid, message = self.validate_fasta_content(filepath)
                    if is_valid:
                        print(f"✅ FASTA验证通过: {message}")
                    else:
                        print(f"⚠️ FASTA验证失败: {message}")
                        # 显示文件内容预览
                        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                            content_preview = f.read(500)
                            print(f"文件内容预览: {content_preview[:200]}...")
                
                file_hash = self.calculate_file_hash(filepath)
                
                # 记录下载信息
                download_info = {
                    'filename': filename,
                    'url': url,
                    'description': description,
                    'download_time': datetime.now().isoformat(),
                    'file_size': file_size,
                    'sha256_hash': file_hash,
                    'status': 'success',
                    'content_type': content_type
                }
                self.download_log.append(download_info)
                
                print(f"✅ 成功下载 {filename} ({file_size:,} bytes)")
                return True
                
            except Exception as e:
                print(f"❌ 下载失败 (尝试 {attempt + 1}): {str(e)}")
                
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    # 记录失败信息
                    download_info = {
                        'filename': filename,
                        'url': url,
                        'description': description,
                        'download_time': datetime.now().isoformat(),
                        'error': str(e),
                        'status': 'failed'
                    }
                    self.download_log.append(download_info)
        
        return False
    
    def download_available_databases(self):
        """下载所有可用的数据库"""
        print("🚀 开始全面的抗菌肽数据收集...")
        print("=" * 70)
        
        # 按优先级分组
        high_priority = {k: v for k, v in self.database_config.items() if v.get('priority') == 'high'}
        medium_priority = {k: v for k, v in self.database_config.items() if v.get('priority') == 'medium'}
        low_priority = {k: v for k, v in self.database_config.items() if v.get('priority') == 'low'}
        
        # 下载高优先级数据库
        print("\n📥 下载高优先级数据库...")
        for db_name, db_config in high_priority.items():
            if db_config['expected_format'] == 'fasta':
                self._download_database(db_name, db_config)
        
        # 处理中优先级数据库（需要手动处理）
        print("\n⚠️ 中优先级数据库（需要手动获取）...")
        for db_name, db_config in medium_priority.items():
            print(f"📋 {db_name}:")
            print(f"   描述: {db_config['description']}")
            print(f"   URL: {db_config['url']}")
            print(f"   说明: {db_config.get('note', '需要手动处理')}")
            print()
        
        # 处理低优先级数据库
        print("\n📋 低优先级数据库（API或特殊处理）...")
        for db_name, db_config in low_priority.items():
            print(f"📋 {db_name}:")
            print(f"   描述: {db_config['description']}")
            print(f"   URL: {db_config['url']}")
            print(f"   说明: {db_config.get('note', '需要特殊处理')}")
            print()
    
    def _download_database(self, db_name, db_config):
        """下载单个数据库"""
        print(f"\n📥 下载 {db_name}...")
        print(f"描述: {db_config['description']}")
        
        filename = f"{db_name}_{datetime.now().strftime('%Y%m%d')}.fasta"
        
        success = self.download_file(
            url=db_config['url'],
            filename=filename,
            description=db_config['description']
        )
        
        if success:
            print(f"✅ {db_name} 下载完成")
        else:
            print(f"❌ {db_name} 下载失败")
        
        print("-" * 50)
    
    def generate_comprehensive_report(self):
        """生成全面的数据收集报告"""
        successful_downloads = [log for log in self.download_log if log['status'] == 'success']
        failed_downloads = [log for log in self.download_log if log['status'] == 'failed']
        
        print("\n" + "=" * 70)
        print("📊 全面数据收集报告")
        print("=" * 70)
        
        print(f"✅ 成功下载: {len(successful_downloads)} 个文件")
        print(f"❌ 下载失败: {len(failed_downloads)} 个文件")
        
        if successful_downloads:
            total_size = sum(log['file_size'] for log in successful_downloads)
            print(f"📦 总数据量: {total_size / (1024 * 1024):.2f} MB")
            
            print("\n📋 成功下载的数据库:")
            for log in successful_downloads:
                print(f"  ✅ {log['filename']}: {log['file_size']:,} bytes")
        
        if failed_downloads:
            print("\n❌ 下载失败的数据库:")
            for log in failed_downloads:
                print(f"  ❌ {log['filename']}: {log.get('error', '未知错误')}")
        
        # 数据完整性评估
        print("\n🔍 数据完整性评估:")
        print("=" * 50)
        
        # 与参考研究对比
        reference_databases = ['APD', 'LAMP1', 'LAMP2', 'BAGEL4', 'dbAMP']
        our_databases = [log['filename'] for log in successful_downloads]
        
        print("📚 参考研究使用的数据库: APD, LAMP1, LAMP2, BAGEL4, dbAMP")
        print("📚 我们已获取的数据库:")
        for db in our_databases:
            print(f"  - {db}")
        
        print("\n⚠️ 缺失的重要数据库:")
        print("  - APD (Antimicrobial Peptide Database) - 需要手动获取")
        print("  - CAMPR4 - 需要通过网页界面导出")
        print("  - LAMP1/LAMP2 - 需要进一步确认数据库性质")
        
        print("\n💡 改进建议:")
        print("  1. 联系APD数据库管理员获取数据访问权限")
        print("  2. 通过CAMPR4网页界面手动导出数据")
        print("  3. 实施序列去重和长度筛选（≤100 AA）")
        print("  4. 考虑使用DBAASP API获取补充数据")
        
        print(f"\n📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def save_download_log(self):
        """保存下载日志"""
        log_file = os.path.join(self.output_dir, f'comprehensive_download_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        # 添加元数据
        metadata = {
            'download_session': {
                'timestamp': datetime.now().isoformat(),
                'total_databases_configured': len(self.database_config),
                'successful_downloads': len([log for log in self.download_log if log['status'] == 'success']),
                'failed_downloads': len([log for log in self.download_log if log['status'] == 'failed']),
                'reference_study_comparison': {
                    'reference_databases': ['APD', 'LAMP1', 'LAMP2', 'BAGEL4', 'dbAMP'],
                    'reference_sequence_count': 24766,
                    'our_coverage_status': 'partial - missing APD and CAMPR4'
                }
            },
            'downloads': self.download_log
        }
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        print(f"📋 详细下载日志已保存到: {log_file}")

def main():
    """主函数"""
    # 设置输出目录
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    output_dir = os.path.join(project_root, 'data', 'raw')
    
    # 创建下载器
    downloader = ComprehensiveAMPDownloader(output_dir)
    
    # 下载所有可用数据库
    downloader.download_available_databases()
    
    # 保存日志和生成报告
    downloader.save_download_log()
    downloader.generate_comprehensive_report()
    
    print("\n🎉 全面数据收集流程完成！")
    print("\n📝 下一步建议:")
    print("  1. 手动获取APD和CAMPR4数据")
    print("  2. 运行数据清洗和去重脚本")
    print("  3. 进行数据质量分析和统计")

if __name__ == "__main__":
    main()
