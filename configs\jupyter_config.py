# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 高算平台Jupyter配置文件，专门为ESM-2抗菌肽项目优化

import os

# Jupyter Lab配置
c = get_config()

# 网络配置
c.ServerApp.ip = '0.0.0.0'
c.ServerApp.port = 8888
c.ServerApp.open_browser = False
c.ServerApp.allow_root = True
c.ServerApp.allow_remote_access = True

# 安全配置（仅在安全的内网环境中使用）
c.ServerApp.token = ''
c.ServerApp.password = ''
c.ServerApp.allow_origin = '*'
c.ServerApp.disable_check_xsrf = True

# 性能配置（针对ESM-2大模型优化）
c.ServerApp.max_buffer_size = 2**30  # 1GB（增加缓冲区）
c.ServerApp.iopub_data_rate_limit = 100000000  # 100MB/s（增加数据传输速率）
c.ServerApp.iopub_msg_rate_limit = 10000  # 增加消息速率限制

# 内核配置
c.MappingKernelManager.default_kernel_name = 'python3'
c.MappingKernelManager.cull_idle_timeout = 0  # 禁用内核自动清理
c.MappingKernelManager.cull_interval = 0  # 禁用清理检查

# 文件管理
c.ServerApp.notebook_dir = os.getcwd()  # 使用当前工作目录

# 日志配置
c.Application.log_level = 'INFO'
c.ServerApp.log_level = 'INFO'

# 扩展配置
c.ServerApp.jpserver_extensions = {
    'jupyter_lsp': True,
    'jupyterlab': True,
}

# 资源限制（针对大模型训练）
c.NotebookApp.max_body_size = 2**32  # 4GB
c.NotebookApp.max_buffer_size = 2**30  # 1GB
c.ServerApp.shutdown_no_activity_timeout = 0  # 禁用自动关闭

# 内核环境变量（PyTorch性能优化）
c.KernelSpecManager.env = {
    'OMP_NUM_THREADS': str(os.environ.get('SLURM_CPUS_PER_TASK', '16')),
    'MKL_NUM_THREADS': str(os.environ.get('SLURM_CPUS_PER_TASK', '16')),
    'NUMEXPR_NUM_THREADS': str(os.environ.get('SLURM_CPUS_PER_TASK', '16')),
    'OPENBLAS_NUM_THREADS': str(os.environ.get('SLURM_CPUS_PER_TASK', '16')),
    'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:128',
    'TOKENIZERS_PARALLELISM': 'false',
    'PYTHONPATH': os.environ.get('PYTHONPATH', ''),
}

print("🔧 Jupyter配置已加载，针对ESM-2训练优化")
print(f"📁 工作目录: {os.getcwd()}")
print(f"🧵 CPU线程数: {os.environ.get('SLURM_CPUS_PER_TASK', '16')}")
