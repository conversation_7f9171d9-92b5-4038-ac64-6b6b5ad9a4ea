# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 高算平台Jupyter配置文件

# Jupyter Lab配置
c = get_config()

# 网络配置
c.ServerApp.ip = '0.0.0.0'
c.ServerApp.port = 8888
c.ServerApp.open_browser = False
c.ServerApp.allow_root = True

# 安全配置（仅在安全的内网环境中使用）
c.ServerApp.token = ''
c.ServerApp.password = ''
c.ServerApp.allow_origin = '*'
c.ServerApp.disable_check_xsrf = True

# 性能配置
c.ServerApp.max_buffer_size = 2**28  # 256MB
c.ServerApp.iopub_data_rate_limit = 10000000  # 10MB/s

# 内核配置
c.MappingKernelManager.default_kernel_name = 'python3'

# 文件管理
c.ServerApp.notebook_dir = '/path/to/your/project'
c.ServerApp.allow_remote_access = True

# 日志配置
c.Application.log_level = 'INFO'

# 扩展配置
c.ServerApp.jpserver_extensions = {
    'jupyter_lsp': True,
    'jupyterlab': True,
}

# 资源限制
c.NotebookApp.max_body_size = 2**30  # 1GB
c.NotebookApp.max_buffer_size = 2**28  # 256MB
