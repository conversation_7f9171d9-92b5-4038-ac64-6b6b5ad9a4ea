# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: 抗菌肽数据集类定义

import torch
from torch.utils.data import Dataset
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tuple
from collections import defaultdict


class AMPDataset(Dataset):
    """抗菌肽数据集类"""
    
    def __init__(self, sequences: List[str], labels: List[int], esm_alphabet, max_length: int = 512):
        """
        初始化数据集
        
        Args:
            sequences: 蛋白质序列列表
            labels: 标签列表 (1=AMP, 0=非AMP)
            esm_alphabet: ESM模型的字母表
            max_length: 最大序列长度
        """
        self.sequences = sequences
        self.labels = labels
        self.esm_alphabet = esm_alphabet
        self.max_length = max_length
        
        # 验证数据
        assert len(sequences) == len(labels), "序列数量与标签数量不匹配"
        
        # 过滤过长的序列
        valid_indices = [i for i, seq in enumerate(sequences) if len(seq) <= max_length]
        self.sequences = [sequences[i] for i in valid_indices]
        self.labels = [labels[i] for i in valid_indices]
        
        print(f"📊 数据集初始化完成:")
        print(f"   总样本数: {len(self.sequences)}")
        print(f"   AMP样本: {sum(self.labels)}")
        print(f"   非AMP样本: {len(self.labels) - sum(self.labels)}")
        print(f"   最大序列长度: {max_length}")
    
    def __len__(self) -> int:
        return len(self.sequences)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """获取单个样本"""
        sequence = self.sequences[idx]
        label = self.labels[idx]
        
        return {
            'sequence': sequence,
            'label': label,
            'length': len(sequence)
        }
    
    def collate_fn(self, batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """批处理函数"""
        sequences = [item['sequence'] for item in batch]
        labels = [item['label'] for item in batch]
        
        # 使用ESM的batch converter
        batch_converter = self.esm_alphabet.get_batch_converter()
        batch_data = [(f"seq_{i}", seq) for i, seq in enumerate(sequences)]
        batch_labels, batch_strs, batch_tokens = batch_converter(batch_data)
        
        return {
            'tokens': batch_tokens,
            'labels': torch.tensor(labels, dtype=torch.float32),
            'sequences': sequences
        }
    
    @classmethod
    def create_splits(cls, sequences: List[str], labels: List[int], 
                     train_ratio: float = 0.7, val_ratio: float = 0.15, 
                     random_state: int = 42) -> Dict[str, Dict[str, List]]:
        """
        创建训练/验证/测试数据划分
        
        Args:
            sequences: 序列列表
            labels: 标签列表
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            random_state: 随机种子
            
        Returns:
            包含train/val/test划分的字典
        """
        from sklearn.model_selection import train_test_split
        
        # 设置随机种子
        np.random.seed(random_state)
        
        # 首先分离出测试集
        test_ratio = 1 - train_ratio - val_ratio
        X_temp, X_test, y_temp, y_test = train_test_split(
            sequences, labels, test_size=test_ratio, 
            random_state=random_state, stratify=labels
        )
        
        # 再从剩余数据中分离训练集和验证集
        val_ratio_adjusted = val_ratio / (train_ratio + val_ratio)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_ratio_adjusted,
            random_state=random_state, stratify=y_temp
        )
        
        splits = {
            'train': {'sequences': X_train, 'labels': y_train},
            'val': {'sequences': X_val, 'labels': y_val},
            'test': {'sequences': X_test, 'labels': y_test}
        }
        
        print(f"📊 数据划分完成:")
        for split_name, split_data in splits.items():
            n_samples = len(split_data['sequences'])
            n_amp = sum(split_data['labels'])
            print(f"   {split_name}: {n_samples} 样本 (AMP: {n_amp}, 非AMP: {n_samples-n_amp})")
        
        return splits


class SequenceProcessor:
    """序列处理工具类"""
    
    @staticmethod
    def clean_sequence(sequence: str) -> str:
        """清理序列，移除非标准氨基酸"""
        # 标准氨基酸字母
        standard_aa = set('ACDEFGHIKLMNPQRSTVWY')
        
        # 过滤非标准字符
        cleaned = ''.join([aa for aa in sequence.upper() if aa in standard_aa])
        
        return cleaned
    
    @staticmethod
    def filter_sequences(sequences: List[str], labels: List[int], 
                        min_length: int = 5, max_length: int = 512) -> Tuple[List[str], List[int]]:
        """
        过滤序列
        
        Args:
            sequences: 序列列表
            labels: 标签列表
            min_length: 最小长度
            max_length: 最大长度
            
        Returns:
            过滤后的序列和标签
        """
        filtered_sequences = []
        filtered_labels = []
        
        for seq, label in zip(sequences, labels):
            # 清理序列
            cleaned_seq = SequenceProcessor.clean_sequence(seq)
            
            # 检查长度
            if min_length <= len(cleaned_seq) <= max_length:
                filtered_sequences.append(cleaned_seq)
                filtered_labels.append(label)
        
        print(f"📊 序列过滤完成:")
        print(f"   原始数量: {len(sequences)}")
        print(f"   过滤后数量: {len(filtered_sequences)}")
        print(f"   过滤比例: {len(filtered_sequences)/len(sequences)*100:.1f}%")
        
        return filtered_sequences, filtered_labels
    
    @staticmethod
    def calculate_sequence_stats(sequences: List[str]) -> Dict[str, Any]:
        """计算序列统计信息"""
        lengths = [len(seq) for seq in sequences]
        
        stats = {
            'count': len(sequences),
            'min_length': min(lengths),
            'max_length': max(lengths),
            'mean_length': np.mean(lengths),
            'median_length': np.median(lengths),
            'std_length': np.std(lengths)
        }
        
        return stats


def load_data_from_files(data_dir: str, file_patterns: Dict[str, str]) -> Tuple[List[str], List[int]]:
    """
    从文件加载数据
    
    Args:
        data_dir: 数据目录
        file_patterns: 文件模式字典
        
    Returns:
        序列列表和标签列表
    """
    import os
    import glob
    from Bio import SeqIO
    
    sequences = []
    labels = []
    
    # 加载AMP序列 (标签=1)
    if 'amp_pattern' in file_patterns:
        amp_files = glob.glob(os.path.join(data_dir, file_patterns['amp_pattern']))
        for file_path in amp_files:
            try:
                for record in SeqIO.parse(file_path, "fasta"):
                    sequences.append(str(record.seq))
                    labels.append(1)
                print(f"✅ 加载AMP文件: {file_path} ({len(list(SeqIO.parse(file_path, 'fasta')))} 序列)")
            except Exception as e:
                print(f"❌ 加载文件失败 {file_path}: {e}")
    
    # 加载非AMP序列 (标签=0)
    if 'non_amp_pattern' in file_patterns:
        non_amp_files = glob.glob(os.path.join(data_dir, file_patterns['non_amp_pattern']))
        for file_path in non_amp_files:
            try:
                for record in SeqIO.parse(file_path, "fasta"):
                    sequences.append(str(record.seq))
                    labels.append(0)
                print(f"✅ 加载非AMP文件: {file_path} ({len(list(SeqIO.parse(file_path, 'fasta')))} 序列)")
            except Exception as e:
                print(f"❌ 加载文件失败 {file_path}: {e}")
    
    print(f"📊 数据加载完成: {len(sequences)} 序列 (AMP: {sum(labels)}, 非AMP: {len(labels)-sum(labels)})")
    
    return sequences, labels
