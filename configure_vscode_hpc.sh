#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: VSCode高算平台自动配置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🔧 VSCode高算平台自动配置"
echo "平台: s20223050931@**************"
echo "=========================================="

# 1. 检查操作系统
print_info "检测操作系统..."
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    SSH_CONFIG_DIR="$HOME/.ssh"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
    SSH_CONFIG_DIR="$HOME/.ssh"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    OS="windows"
    SSH_CONFIG_DIR="$HOME/.ssh"
else
    print_warning "未识别的操作系统: $OSTYPE"
    SSH_CONFIG_DIR="$HOME/.ssh"
fi
print_success "操作系统: $OS"

# 2. 创建SSH目录和socket目录
print_info "创建SSH配置目录..."
mkdir -p "$SSH_CONFIG_DIR"
mkdir -p "$SSH_CONFIG_DIR/sockets"
print_success "SSH目录创建完成"

# 3. 配置SSH
print_info "配置SSH连接..."
SSH_CONFIG_FILE="$SSH_CONFIG_DIR/config"

# 检查是否已有配置
if [ -f "$SSH_CONFIG_FILE" ]; then
    if grep -q "**************" "$SSH_CONFIG_FILE"; then
        print_warning "SSH配置已存在，将备份原配置"
        cp "$SSH_CONFIG_FILE" "$SSH_CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    fi
fi

# 添加高算平台配置
print_info "添加高算平台SSH配置..."
cat >> "$SSH_CONFIG_FILE" << 'EOF'

# ========================================
# ESM-2抗菌肽项目 - 高算平台配置
# 自动生成于 2025-01-27
# ========================================

# 高算平台主连接
Host hpc-main
    HostName **************
    User s20223050931
    Port 22
    ServerAliveInterval 30
    ServerAliveCountMax 5
    TCPKeepAlive yes
    Compression yes
    ControlMaster auto
    ControlPath ~/.ssh/sockets/%r@%h-%p
    ControlPersist 600
    ClientAliveInterval 60
    ClientAliveCountMax 3

# 直接连接（备用）
Host hpc-direct
    HostName **************
    User s20223050931
    Port 22
    ServerAliveInterval 30
    ServerAliveCountMax 5
    Compression yes

# Jupyter端口转发配置
Host hpc-jupyter
    HostName **************
    User s20223050931
    Port 22
    LocalForward 8888 localhost:8888
    LocalForward 8889 localhost:8889
    LocalForward 8890 localhost:8890
    ServerAliveInterval 30
    ServerAliveCountMax 5
    Compression yes

EOF

print_success "SSH配置添加完成"

# 4. 设置文件权限
print_info "设置SSH文件权限..."
chmod 700 "$SSH_CONFIG_DIR"
chmod 600 "$SSH_CONFIG_FILE"
chmod 700 "$SSH_CONFIG_DIR/sockets"
print_success "文件权限设置完成"

# 5. 给项目脚本添加执行权限
print_info "设置项目脚本权限..."
chmod +x scripts/setup_hpc_environment.sh
chmod +x scripts/run_jupyter_hpc.sh
chmod +x scripts/vscode_hpc_integration.sh
chmod +x scripts/verify_esm_compatibility.py
chmod +x deploy_to_hpc.sh
chmod +x quick_start_vscode.sh
print_success "脚本权限设置完成"

# 6. 测试SSH连接
print_info "测试SSH连接..."
echo "正在测试连接到 **************..."

if timeout 10 ssh -o ConnectTimeout=10 -o BatchMode=yes hpc-main "echo 'SSH连接测试成功'" 2>/dev/null; then
    print_success "SSH连接测试成功！"
else
    print_warning "SSH连接测试失败，可能需要手动输入密码或配置密钥"
    print_info "请尝试手动连接: ssh hpc-main"
fi

# 7. 显示VSCode配置信息
print_info "VSCode配置信息..."
echo "已创建的VSCode配置文件:"
echo "  ✅ .vscode/settings.json - 项目设置"
echo "  ✅ .vscode/tasks.json - 任务配置"
echo "  ✅ .vscode/launch.json - 调试配置"
echo "  ✅ .vscode/extensions.json - 扩展推荐"

# 8. 显示使用说明
echo ""
echo "=========================================="
print_success "🎉 VSCode高算平台配置完成！"
echo "=========================================="
echo ""
echo "📋 下一步操作:"
echo "1. 打开VSCode"
echo "2. 安装推荐的扩展（VSCode会自动提示）"
echo "3. 按 Ctrl+Shift+P，输入 'Remote-SSH: Connect to Host'"
echo "4. 选择 'hpc-main' 连接到高算平台"
echo "5. 在VSCode终端中运行: ./scripts/vscode_hpc_integration.sh"
echo ""
echo "🔧 可用的VSCode任务 (Ctrl+Shift+P → Tasks: Run Task):"
echo "  - 🚀 启动高算平台Jupyter"
echo "  - 🔧 配置高算环境"
echo "  - 🧪 验证ESM兼容性"
echo "  - 📊 检查作业状态"
echo "  - 🧹 清理环境"
echo ""
echo "📖 详细指南: docs/vscode_hpc_guide.md"
echo ""
echo "💡 提示:"
echo "  - 首次连接可能需要输入密码"
echo "  - 建议配置SSH密钥以免密登录"
echo "  - 如遇问题，请查看 ~/.ssh/config 文件"
echo ""
print_success "配置完成！现在可以在VSCode中使用高算平台了！"
