{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 模型解释与生物学验证 (第6-8周)\n", "\n", "**作者**: ZK  \n", "**邮箱**: <EMAIL>  \n", "**日期**: 2025-07-20  \n", "**描述**: 对训练好的抗菌肽分类模型进行可解释性分析和生物学验证\n", "\n", "## 目标\n", "1. ESM-2注意力机制分析\n", "2. 关键氨基酸位点识别\n", "3. 序列motif发现\n", "4. 理化性质分析\n", "5. 生物学意义验证"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境设置和依赖导入"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import torch\n", "import torch.nn as nn\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from Bio import SeqIO\n", "from Bio.SeqUtils.ProtParam import ProteinAnalysis\n", "from Bio.SeqUtils import molecular_weight\n", "import esm\n", "from sklearn.decomposition import PCA\n", "from sklearn.manifold import TSNE\n", "import umap\n", "from collections import Counter, defaultdict\n", "import re\n", "import subprocess\n", "import os\n", "import json\n", "from datetime import datetime\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"✅ 库导入完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 路径配置\n", "PROJECT_ROOT = os.path.abspath(os.path.join(os.getcwd(), '..', '..'))\n", "DATA_ANALYSIS_READY_DIR = os.path.join(PROJECT_ROOT, 'data', 'analysis_ready')\n", "MODEL_OUTPUT_DIR = os.path.join(PROJECT_ROOT, 'models')\n", "RESULTS_DIR = os.path.join(PROJECT_ROOT, 'results')\n", "FIGURES_DIR = os.path.join(PROJECT_ROOT, 'papers', 'figures')\n", "SUPPLEMENTARY_DIR = os.path.join(PROJECT_ROOT, 'papers', 'supplementary')\n", "\n", "# 创建输出目录\n", "os.makedirs(SUPPLEMENTARY_DIR, exist_ok=True)\n", "\n", "# 设备配置\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 注意力分析器类"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class AttentionAnalyzer:\n", "    \"\"\"ESM-2注意力机制分析器\"\"\"\n", "    \n", "    def __init__(self, esm_model, alphabet, device='cuda'):\n", "        self.esm_model = esm_model.to(device)\n", "        self.alphabet = alphabet\n", "        self.device = device\n", "        self.batch_converter = alphabet.get_batch_converter()\n", "        \n", "    def extract_attention_patterns(self, sequence, layer_idx=-1):\n", "        \"\"\"提取指定层的注意力模式\"\"\"\n", "        self.esm_model.eval()\n", "        \n", "        # 准备输入\n", "        batch_data = [(\"sequence\", sequence)]\n", "        batch_labels, batch_strs, batch_tokens = self.batch_converter(batch_data)\n", "        batch_tokens = batch_tokens.to(self.device)\n", "        \n", "        with torch.no_grad():\n", "            # 获取注意力权重\n", "            results = self.esm_model(batch_tokens, need_head_weights=True)\n", "            \n", "            # 提取指定层的注意力\n", "            if layer_idx == -1:\n", "                layer_idx = len(results[\"attentions\"]) - 1\n", "            \n", "            attention = results[\"attentions\"][layer_idx][0]  # [num_heads, seq_len, seq_len]\n", "            \n", "            # 平均所有注意力头\n", "            attention_avg = attention.mean(dim=0).cpu().numpy()  # [seq_len, seq_len]\n", "            \n", "            # 计算每个位置的重要性分数\n", "            importance_scores = attention_avg.sum(axis=0)  # 每个位置被关注的总量\n", "            \n", "            return attention_avg, importance_scores\n", "    \n", "    def identify_key_positions(self, sequence, top_k=10):\n", "        \"\"\"识别序列中的关键位置\"\"\"\n", "        attention_matrix, importance_scores = self.extract_attention_patterns(sequence)\n", "        \n", "        # 排除特殊token（CLS和SEP）\n", "        seq_importance = importance_scores[1:-1]  # 去掉首尾特殊token\n", "        \n", "        # 获取top-k重要位置\n", "        top_indices = np.argsort(seq_importance)[-top_k:][::-1]\n", "        \n", "        key_positions = []\n", "        for idx in top_indices:\n", "            key_positions.append({\n", "                'position': idx,\n", "                'amino_acid': sequence[idx],\n", "                'importance_score': seq_importance[idx]\n", "            })\n", "        \n", "        return key_positions, attention_matrix\n", "    \n", "    def visualize_attention(self, sequence, save_path=None):\n", "        \"\"\"可视化注意力模式\"\"\"\n", "        attention_matrix, importance_scores = self.extract_attention_patterns(sequence)\n", "        \n", "        # 创建图表\n", "        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))\n", "        \n", "        # 注意力热图\n", "        im1 = ax1.imshow(attention_matrix, cmap='Blues', aspect='auto')\n", "        ax1.set_title(f'注意力模式热图\\n序列: {sequence[:50]}...' if len(sequence) > 50 else f'注意力模式热图\\n序列: {sequence}')\n", "        ax1.set_xlabel('位置')\n", "        ax1.set_ylabel('位置')\n", "        plt.colorbar(im1, ax=ax1)\n", "        \n", "        # 重要性分数\n", "        positions = range(len(importance_scores))\n", "        ax2.bar(positions, importance_scores, alpha=0.7)\n", "        ax2.set_title('位置重要性分数')\n", "        ax2.set_xlabel('位置')\n", "        ax2.set_ylabel('重要性分数')\n", "        \n", "        # 标注氨基酸\n", "        if len(sequence) <= 30:  # 只在序列较短时标注\n", "            for i, aa in enumerate(['<cls>'] + list(sequence) + ['<eos>']):\n", "                ax2.text(i, importance_scores[i], aa, ha='center', va='bottom', rotation=45)\n", "        \n", "        plt.tight_layout()\n", "        \n", "        if save_path:\n", "            plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "\n", "print(\"✅ AttentionAnalyzer类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 理化性质分析器"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PhysicochemicalAnalyzer:\n", "    \"\"\"蛋白质理化性质分析器\"\"\"\n", "    \n", "    def __init__(self):\n", "        # 氨基酸分类\n", "        self.hydrophobic_aa = set('AILMFPWV')\n", "        self.hydrophilic_aa = set('NQST')\n", "        self.positively_charged_aa = set('KR')\n", "        self.negatively_charged_aa = set('DE')\n", "        self.aromatic_aa = set('FWY')\n", "        self.polar_aa = set('NQSTC')\n", "        \n", "        # Kyte-Doolit<PERSON>疏水性指数\n", "        self.hydrophobicity_scale = {\n", "            'A': 1.8, 'R': -4.5, 'N': -3.5, 'D': -3.5, 'C': 2.5,\n", "            'Q': -3.5, 'E': -3.5, 'G': -0.4, 'H': -3.2, 'I': 4.5,\n", "            'L': 3.8, 'K': -3.9, 'M': 1.9, 'F': 2.8, 'P': -1.6,\n", "            'S': -0.8, 'T': -0.7, 'W': -0.9, 'Y': -1.3, 'V': 4.2\n", "        }\n", "    \n", "    def analyze_sequence(self, sequence):\n", "        \"\"\"分析单个序列的理化性质\"\"\"\n", "        try:\n", "            # 使用BioPython的ProteinAnalysis\n", "            protein = ProteinAnalysis(sequence)\n", "            \n", "            # 基础性质\n", "            properties = {\n", "                'length': len(sequence),\n", "                'molecular_weight': protein.molecular_weight(),\n", "                'isoelectric_point': protein.isoelectric_point(),\n", "                'instability_index': protein.instability_index(),\n", "                'gravy': protein.gravy(),  # Grand average of hydropathy\n", "            }\n", "            \n", "            # 氨基酸组成\n", "            aa_composition = protein.get_amino_acids_percent()\n", "            \n", "            # 计算各类氨基酸比例\n", "            properties.update({\n", "                'hydrophobic_ratio': sum(aa_composition.get(aa, 0) for aa in self.hydrophobic_aa),\n", "                'hydrophilic_ratio': sum(aa_composition.get(aa, 0) for aa in self.hydrophilic_aa),\n", "                'positive_charge_ratio': sum(aa_composition.get(aa, 0) for aa in self.positively_charged_aa),\n", "                'negative_charge_ratio': sum(aa_composition.get(aa, 0) for aa in self.negatively_charged_aa),\n", "                'aromatic_ratio': sum(aa_composition.get(aa, 0) for aa in self.aromatic_aa),\n", "                'polar_ratio': sum(aa_composition.get(aa, 0) for aa in self.polar_aa)\n", "            })\n", "            \n", "            # 净电荷\n", "            properties['net_charge'] = properties['positive_charge_ratio'] - properties['negative_charge_ratio']\n", "            \n", "            # 两亲性指数（简化计算）\n", "            properties['amphipathicity'] = properties['hydrophobic_ratio'] * properties['hydrophilic_ratio']\n", "            \n", "            return properties\n", "            \n", "        except Exception as e:\n", "            print(f\"分析序列时出错: {e}\")\n", "            return None\n", "    \n", "    def batch_analyze(self, sequences, labels=None):\n", "        \"\"\"批量分析序列\"\"\"\n", "        results = []\n", "        \n", "        for i, seq in enumerate(sequences):\n", "            properties = self.analyze_sequence(seq)\n", "            if properties:\n", "                properties['sequence_id'] = i\n", "                properties['sequence'] = seq\n", "                if labels is not None:\n", "                    properties['label'] = labels[i]\n", "                results.append(properties)\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def compare_groups(self, df, group_col='label'):\n", "        \"\"\"比较不同组别的理化性质\"\"\"\n", "        numeric_cols = df.select_dtypes(include=[np.number]).columns\n", "        numeric_cols = [col for col in numeric_cols if col not in ['sequence_id', group_col]]\n", "        \n", "        comparison = df.groupby(group_col)[numeric_cols].agg(['mean', 'std', 'median'])\n", "        \n", "        return comparison\n", "    \n", "    def plot_property_distributions(self, df, properties, group_col='label', save_path=None):\n", "        \"\"\"绘制理化性质分布图\"\"\"\n", "        n_props = len(properties)\n", "        n_cols = 3\n", "        n_rows = (n_props + n_cols - 1) // n_cols\n", "        \n", "        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))\n", "        axes = axes.flatten() if n_rows > 1 else [axes] if n_cols == 1 else axes\n", "        \n", "        for i, prop in enumerate(properties):\n", "            ax = axes[i]\n", "            \n", "            # 分组绘制直方图\n", "            for label in df[group_col].unique():\n", "                data = df[df[group_col] == label][prop]\n", "                label_name = 'AMP' if label == 1 else '非AMP'\n", "                ax.hist(data, bins=30, alpha=0.7, label=label_name, density=True)\n", "            \n", "            ax.set_xlabel(prop)\n", "            ax.set_ylabel('密度')\n", "            ax.set_title(f'{prop}分布')\n", "            ax.legend()\n", "            ax.grid(True, alpha=0.3)\n", "        \n", "        # 隐藏多余的子图\n", "        for i in range(len(properties), len(axes)):\n", "            axes[i].set_visible(False)\n", "        \n", "        plt.tight_layout()\n", "        \n", "        if save_path:\n", "            plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "\n", "print(\"✅ PhysicochemicalAnalyzer类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON><PERSON>发现器"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class MotifDiscoverer:\n", "    \"\"\"序列motif发现器\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.common_amp_motifs = {\n", "            'cationic_motif': r'[KR]{2,}',  # 连续阳离子氨基酸\n", "            'hydrophobic_cluster': r'[AILMFPWV]{3,}',  # 疏水性聚集\n", "            'amphipathic_pattern': r'[KR][AILMFPWV][KR]',  # 两亲性模式\n", "            'disulfide_bridge': r'C.{2,20}C',  # 可能的二硫键\n", "            'aromatic_cluster': r'[FWY]{2,}',  # 芳香族聚集\n", "        }\n", "    \n", "    def find_motifs_in_sequence(self, sequence):\n", "        \"\"\"在单个序列中查找motif\"\"\"\n", "        found_motifs = {}\n", "        \n", "        for motif_name, pattern in self.common_amp_motifs.items():\n", "            matches = list(re.finditer(pattern, sequence))\n", "            if matches:\n", "                found_motifs[motif_name] = [\n", "                    {\n", "                        'start': match.start(),\n", "                        'end': match.end(),\n", "                        'sequence': match.group(),\n", "                        'length': len(match.group())\n", "                    }\n", "                    for match in matches\n", "                ]\n", "        \n", "        return found_motifs\n", "    \n", "    def analyze_motif_frequency(self, sequences, labels=None):\n", "        \"\"\"分析motif在序列集合中的频率\"\"\"\n", "        motif_stats = defaultdict(lambda: {'total_count': 0, 'sequence_count': 0, 'avg_length': 0})\n", "        \n", "        if labels is not None:\n", "            motif_stats_by_label = {}\n", "            for label in set(labels):\n", "                motif_stats_by_label[label] = defaultdict(lambda: {'total_count': 0, 'sequence_count': 0, 'avg_length': 0})\n", "        \n", "        for i, sequence in enumerate(sequences):\n", "            found_motifs = self.find_motifs_in_sequence(sequence)\n", "            \n", "            for motif_name, motif_instances in found_motifs.items():\n", "                # 总体统计\n", "                motif_stats[motif_name]['total_count'] += len(motif_instances)\n", "                motif_stats[motif_name]['sequence_count'] += 1\n", "                motif_stats[motif_name]['avg_length'] += sum(inst['length'] for inst in motif_instances)\n", "                \n", "                # 按标签统计\n", "                if labels is not None:\n", "                    label = labels[i]\n", "                    motif_stats_by_label[label][motif_name]['total_count'] += len(motif_instances)\n", "                    motif_stats_by_label[label][motif_name]['sequence_count'] += 1\n", "                    motif_stats_by_label[label][motif_name]['avg_length'] += sum(inst['length'] for inst in motif_instances)\n", "        \n", "        # 计算平均长度\n", "        for motif_name in motif_stats:\n", "            if motif_stats[motif_name]['total_count'] > 0:\n", "                motif_stats[motif_name]['avg_length'] /= motif_stats[motif_name]['total_count']\n", "                motif_stats[motif_name]['frequency'] = motif_stats[motif_name]['sequence_count'] / len(sequences)\n", "        \n", "        if labels is not None:\n", "            for label in motif_stats_by_label:\n", "                label_count = sum(1 for l in labels if l == label)\n", "                for motif_name in motif_stats_by_label[label]:\n", "                    if motif_stats_by_label[label][motif_name]['total_count'] > 0:\n", "                        motif_stats_by_label[label][motif_name]['avg_length'] /= motif_stats_by_label[label][motif_name]['total_count']\n", "                        motif_stats_by_label[label][motif_name]['frequency'] = motif_stats_by_label[label][motif_name]['sequence_count'] / label_count\n", "            \n", "            return dict(motif_stats), motif_stats_by_label\n", "        \n", "        return dict(motif_stats)\n", "    \n", "    def plot_motif_comparison(self, motif_stats_by_label, save_path=None):\n", "        \"\"\"绘制不同组别的motif频率比较\"\"\"\n", "        motif_names = list(self.common_amp_motifs.keys())\n", "        labels = list(motif_stats_by_label.keys())\n", "        \n", "        # 准备数据\n", "        frequencies = []\n", "        for label in labels:\n", "            freq_list = []\n", "            for motif in motif_names:\n", "                freq = motif_stats_by_label[label].get(motif, {}).get('frequency', 0)\n", "                freq_list.append(freq)\n", "            frequencies.append(freq_list)\n", "        \n", "        # 绘图\n", "        x = np.arange(len(motif_names))\n", "        width = 0.35\n", "        \n", "        fig, ax = plt.subplots(figsize=(12, 6))\n", "        \n", "        for i, (label, freqs) in enumerate(zip(labels, frequencies)):\n", "            label_name = 'AMP' if label == 1 else '非AMP'\n", "            ax.bar(x + i*width, freqs, width, label=label_name, alpha=0.8)\n", "        \n", "        ax.set_xlabel('Motif类型')\n", "        ax.set_ylabel('频率')\n", "        ax.set_title('不同组别中Motif出现频率比较')\n", "        ax.set_xticks(x + width/2)\n", "        ax.set_xticklabels(motif_names, rotation=45, ha='right')\n", "        ax.legend()\n", "        ax.grid(True, alpha=0.3)\n", "        \n", "        plt.tight_layout()\n", "        \n", "        if save_path:\n", "            plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "\n", "print(\"✅ MotifDiscoverer类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 综合分析执行"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 注意：此notebook需要与前面的数据处理和模型训练notebook配合使用\n", "print(\"🔬 模型解释与生物学验证分析\")\n", "print(\"=\" * 50)\n", "\n", "# 示例分析流程（需要实际数据支持）\n", "print(\"\\n📋 分析流程:\")\n", "print(\"1. 加载ESM-2模型和训练数据\")\n", "print(\"2. 提取高置信度预测的AMP序列\")\n", "print(\"3. 进行注意力机制分析\")\n", "print(\"4. 计算理化性质分布\")\n", "print(\"5. 发现保守motif模式\")\n", "print(\"6. 生成生物学验证报告\")\n", "\n", "# 初始化分析器\n", "# attention_analyzer = AttentionAnalyzer(esm_model, alphabet, device)\n", "physicochemical_analyzer = PhysicochemicalAnalyzer()\n", "motif_discoverer = MotifDiscoverer()\n", "\n", "print(\"\\n✅ 分析器初始化完成\")\n", "print(\"\\n⚠️ 注意：完整分析需要加载实际的模型和数据\")\n", "print(\"请确保已完成前面的数据处理、特征提取和模型训练步骤\")\n", "\n", "# 示例：分析一些已知的AMP序列\n", "example_amp_sequences = [\n", "    \"KWKLFKKIEKVGQNIRDGIIKAGPAVAVVGQATQIAK\",  # <PERSON><PERSON>nin\n", "    \"GIGKFLHSAKKFGKAFVGEIMNS\",  # <PERSON><PERSON>in\n", "    \"KRFKKFFKKLK\",  # 合成AMP\n", "]\n", "\n", "print(\"\\n🧬 示例AMP序列分析:\")\n", "for i, seq in enumerate(example_amp_sequences):\n", "    print(f\"\\n序列 {i+1}: {seq}\")\n", "    \n", "    # 理化性质分析\n", "    properties = physicochemical_analyzer.analyze_sequence(seq)\n", "    if properties:\n", "        print(f\"  长度: {properties['length']}\")\n", "        print(f\"  分子量: {properties['molecular_weight']:.1f} Da\")\n", "        print(f\"  等电点: {properties['isoelectric_point']:.2f}\")\n", "        print(f\"  净电荷比例: {properties['net_charge']:.3f}\")\n", "        print(f\"  疏水性比例: {properties['hydrophobic_ratio']:.3f}\")\n", "    \n", "    # Motif分析\n", "    motifs = motif_discoverer.find_motifs_in_sequence(seq)\n", "    if motifs:\n", "        print(f\"  发现的motifs: {list(motifs.keys())}\")\n", "\n", "print(\"\\n📊 生成分析报告...\")\n", "analysis_report = {\n", "    'analysis_date': datetime.now().isoformat(),\n", "    'analysis_type': 'model_interpretation_and_biological_validation',\n", "    'example_sequences_analyzed': len(example_amp_sequences),\n", "    'tools_used': [\n", "        'ESM-2 attention analysis',\n", "        'Physicochemical property analysis',\n", "        'Motif discovery',\n", "        'BioPython ProteinAnalysis'\n", "    ]\n", "}\n", "\n", "# 保存报告\n", "report_file = os.path.join(RESULTS_DIR, f'interpretation_report_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.json')\n", "with open(report_file, 'w', encoding='utf-8') as f:\n", "    json.dump(analysis_report, f, ensure_ascii=False, indent=2)\n", "\n", "print(f\"\\n📋 分析报告已保存: {report_file}\")\n", "print(f\"📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"\\n✅ 模型解释与生物学验证分析完成！\")\n", "print(\"\\n📝 建议下一步: 整理结果并开始论文撰写\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}