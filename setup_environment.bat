@echo off
REM 抗菌肽项目环境配置脚本 (Windows版本)
REM 作者: ZK
REM 邮箱: <EMAIL>
REM 日期: 2025-07-20
REM 描述: 自动配置抗菌肽分类项目的完整Python环境

echo 🚀 开始配置抗菌肽项目环境...
echo ==================================

REM 检查conda是否安装
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到conda，请先安装Anaconda或Miniconda
    echo 下载地址: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo ✅ 检测到conda
conda --version

REM 检查CUDA版本
where nvidia-smi >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo 🔍 检测GPU信息:
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits
    set CUDA_AVAILABLE=1
) else (
    echo ⚠️  未检测到NVIDIA GPU，将安装CPU版本
    set CUDA_AVAILABLE=0
)

REM 创建conda环境
echo 📦 创建conda环境: amp_esm2
conda create -n amp_esm2 python=3.9 -y

REM 激活环境
echo 🔄 激活环境...
call conda activate amp_esm2

REM 安装PyTorch
echo 🔥 安装PyTorch...
if %CUDA_AVAILABLE%==1 (
    conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
) else (
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
)

REM 安装生物信息学工具
echo 🧬 安装生物信息学工具...
conda install -c bioconda biopython -y

REM 安装基础科学计算包
echo 📊 安装科学计算包...
conda install numpy pandas scikit-learn matplotlib seaborn jupyter ipykernel -y

REM 安装深度学习相关包
echo 🤖 安装深度学习包...
pip install fair-esm transformers datasets

REM 安装可视化和实验跟踪包
echo 📈 安装可视化包...
pip install umap-learn plotly kaleido wandb tensorboard

REM 安装其他实用包
echo 🛠️ 安装实用工具...
pip install tqdm requests openpyxl xlsxwriter

REM 注册jupyter kernel
echo 📓 配置Jupyter kernel...
python -m ipykernel install --user --name amp_esm2 --display-name "AMP ESM-2"

REM 验证安装
echo 🔍 验证安装...
python -c "import torch; import numpy as np; import pandas as pd; from Bio import SeqIO; import sklearn; import matplotlib.pyplot as plt; print('✅ PyTorch版本:', torch.__version__); print('✅ CUDA可用:', torch.cuda.is_available()); print('✅ 所有核心包安装成功')"

REM 创建requirements.txt
echo 📝 生成requirements.txt...
pip freeze > requirements.txt

echo.
echo 🎉 环境配置完成！
echo ==================================
echo 环境名称: amp_esm2
echo.
echo 🚀 使用方法:
echo 1. 激活环境: conda activate amp_esm2
echo 2. 启动Jupyter: jupyter notebook
echo 3. 选择kernel: AMP ESM-2
echo.
echo 📁 开始运行项目:
echo cd scripts\data_download
echo jupyter notebook 01_数据收集与下载.ipynb
echo.
echo 💾 环境已保存到 requirements.txt
pause
