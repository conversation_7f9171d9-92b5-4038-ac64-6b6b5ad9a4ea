# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-20
# 描述: APD数据库下载器 - 尝试多种方法获取APD数据

import os
import requests
import time
from datetime import datetime
import json
from urllib.parse import urljoin
import re

class APDDownloader:
    """APD数据库下载器"""
    
    def __init__(self, output_dir):
        self.output_dir = output_dir
        self.base_url = "https://aps.unmc.edu"
        self.download_log = []
        
        # 可能的下载URL
        self.potential_urls = [
            "https://aps.unmc.edu/downloads",
            "https://aps.unmc.edu/download",
            "https://aps.unmc.edu/AP/download",
            "https://aps.unmc.edu/AP/downloads",
            "https://aps.unmc.edu/data",
            "https://aps.unmc.edu/fasta",
            "https://aps.unmc.edu/sequences",
        ]
        
        # 可能的FASTA文件名
        self.potential_files = [
            "APD_sequences.fasta",
            "APD_all.fasta",
            "APD3_sequences.fasta",
            "APD3_all.fasta",
            "natural_AMPs.fasta",
            "antimicrobial_peptides.fasta",
            "APD_2024.fasta",
            "APD3_2024.fasta",
        ]
        
        os.makedirs(output_dir, exist_ok=True)
    
    def test_connection(self, url, timeout=30):
        """测试URL连接"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            response = requests.get(url, headers=headers, timeout=timeout, verify=False)
            return response.status_code, response.text[:500]
            
        except requests.exceptions.Timeout:
            return None, "连接超时"
        except requests.exceptions.ConnectionError:
            return None, "连接错误"
        except Exception as e:
            return None, f"其他错误: {str(e)}"
    
    def try_direct_download(self, base_url, filename):
        """尝试直接下载文件"""
        url = urljoin(base_url, filename)
        
        try:
            print(f"   尝试下载: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/octet-stream,text/plain,*/*',
            }
            
            response = requests.get(url, headers=headers, timeout=60, stream=True)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '').lower()
                content_length = response.headers.get('content-length', 0)
                
                print(f"   ✅ 响应成功: {response.status_code}")
                print(f"   Content-Type: {content_type}")
                print(f"   Content-Length: {content_length}")
                
                # 检查是否是FASTA文件
                if 'text' in content_type or 'fasta' in content_type or int(content_length) > 1000:
                    output_file = os.path.join(self.output_dir, f"APD_{filename}")
                    
                    with open(output_file, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                    
                    file_size = os.path.getsize(output_file)
                    print(f"   ✅ 下载成功: {output_file} ({file_size:,} bytes)")
                    
                    # 验证文件内容
                    with open(output_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content_preview = f.read(200)
                        if content_preview.startswith('>') or 'FASTA' in content_preview.upper():
                            print(f"   ✅ 确认为FASTA格式")
                            return True, output_file
                        else:
                            print(f"   ⚠️ 文件格式可疑: {content_preview[:100]}...")
                            return False, "文件格式不是FASTA"
                else:
                    print(f"   ❌ 响应不是文件: {content_type}")
                    return False, f"不是文件类型: {content_type}"
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                return False, f"HTTP {response.status_code}"
                
        except Exception as e:
            print(f"   ❌ 下载失败: {str(e)}")
            return False, str(e)
    
    def scan_for_download_links(self, url):
        """扫描页面寻找下载链接"""
        try:
            print(f"📄 扫描页面: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                content = response.text
                
                # 寻找FASTA相关链接
                fasta_links = re.findall(r'href=["\']([^"\']*\.fasta[^"\']*)["\']', content, re.IGNORECASE)
                download_links = re.findall(r'href=["\']([^"\']*download[^"\']*)["\']', content, re.IGNORECASE)
                
                print(f"   找到FASTA链接: {len(fasta_links)}个")
                print(f"   找到下载链接: {len(download_links)}个")
                
                all_links = list(set(fasta_links + download_links))
                
                for link in all_links[:5]:  # 只尝试前5个
                    if not link.startswith('http'):
                        link = urljoin(url, link)
                    
                    print(f"   尝试链接: {link}")
                    success, result = self.try_direct_download(link, os.path.basename(link))
                    
                    if success:
                        return True, result
                
                return False, "未找到有效的下载链接"
            else:
                return False, f"页面访问失败: {response.status_code}"
                
        except Exception as e:
            return False, f"页面扫描失败: {str(e)}"
    
    def attempt_apd_download(self):
        """尝试下载APD数据"""
        print("🚀 开始尝试下载APD数据库...")
        print("=" * 60)
        
        # 方法1: 测试基础连接
        print(f"\n📡 测试APD网站连接...")
        status_code, content = self.test_connection(self.base_url)
        
        if status_code:
            print(f"   ✅ 网站可访问: HTTP {status_code}")
            print(f"   页面预览: {content[:100]}...")
        else:
            print(f"   ❌ 网站无法访问: {content}")
            print(f"   可能原因: 网络限制、服务器维护、或需要特殊访问权限")
        
        # 方法2: 尝试已知的下载页面
        print(f"\n📥 尝试访问下载页面...")
        for url in self.potential_urls:
            print(f"\n🔍 尝试URL: {url}")
            
            status_code, content = self.test_connection(url)
            
            if status_code == 200:
                print(f"   ✅ 页面可访问")
                
                # 扫描页面寻找下载链接
                success, result = self.scan_for_download_links(url)
                if success:
                    print(f"   🎉 成功下载: {result}")
                    return True, result
                else:
                    print(f"   ⚠️ 未找到下载链接: {result}")
            
            elif status_code:
                print(f"   ❌ HTTP错误: {status_code}")
            else:
                print(f"   ❌ 连接失败: {content}")
            
            time.sleep(2)  # 避免请求过快
        
        # 方法3: 尝试直接下载常见文件名
        print(f"\n📁 尝试直接下载常见文件...")
        for base_url in [self.base_url, f"{self.base_url}/downloads", f"{self.base_url}/data"]:
            for filename in self.potential_files:
                success, result = self.try_direct_download(base_url, filename)
                if success:
                    print(f"   🎉 成功下载: {result}")
                    return True, result
                
                time.sleep(1)  # 避免请求过快
        
        return False, "所有下载方法都失败了"
    
    def provide_manual_instructions(self):
        """提供手动下载指导"""
        print(f"\n" + "=" * 60)
        print("📋 APD数据库手动下载指导")
        print("=" * 60)
        
        print(f"\n🌐 由于自动下载失败，请尝试以下手动方法:")
        
        print(f"\n方法1 - 直接访问:")
        print(f"   1. 打开浏览器访问: https://aps.unmc.edu/downloads")
        print(f"   2. 查找 'FASTA format' 或 'Sequence Downloads' 链接")
        print(f"   3. 下载所有可用的FASTA文件")
        print(f"   4. 将文件保存到: {self.output_dir}")
        
        print(f"\n方法2 - 联系数据库管理员:")
        print(f"   1. 发送邮件到APD数据库管理员")
        print(f"   2. 请求获取完整的AMP序列数据")
        print(f"   3. 说明用于学术研究目的")
        
        print(f"\n方法3 - 使用替代数据源:")
        print(f"   1. 我们已经有23,576个抗细菌肽序列")
        print(f"   2. 这个数量已经足够进行高质量的模型训练")
        print(f"   3. 可以先用现有数据开始，后续再补充APD数据")
        
        print(f"\n💡 建议:")
        print(f"   考虑到我们已经有充足的高质量抗细菌肽数据，")
        print(f"   建议先使用现有的23,576个序列开始模型训练。")
        print(f"   这个数量已经超过了大多数研究的标准。")
    
    def save_attempt_log(self):
        """保存尝试日志"""
        log_file = os.path.join(self.output_dir, f'apd_download_attempt_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        log_data = {
            'attempt_timestamp': datetime.now().isoformat(),
            'base_url': self.base_url,
            'attempted_urls': self.potential_urls,
            'attempted_files': self.potential_files,
            'result': 'failed',
            'recommendation': 'use_existing_data_or_manual_download'
        }
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 尝试日志已保存到: {log_file}")

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    output_dir = os.path.join(project_root, 'data', 'raw')
    
    # 创建下载器
    downloader = APDDownloader(output_dir)
    
    # 尝试下载
    success, result = downloader.attempt_apd_download()
    
    if success:
        print(f"\n🎉 APD数据下载成功!")
        print(f"📁 文件位置: {result}")
    else:
        print(f"\n❌ APD数据自动下载失败: {result}")
        
        # 提供手动下载指导
        downloader.provide_manual_instructions()
    
    # 保存尝试日志
    downloader.save_attempt_log()

if __name__ == "__main__":
    main()
