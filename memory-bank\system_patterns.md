# 系统设计模式与最佳实践

## 项目架构模式

### 1. 分层架构 (Layered Architecture)

```
抗菌肽文章撰写/
├── 数据层 (Data Layer)
│   ├── data/raw/           # 原始数据存储
│   ├── data/processed/     # 清洗后数据
│   └── data/analysis_ready/ # 分析就绪数据
├── 处理层 (Processing Layer)
│   ├── scripts/data_download/  # 数据获取
│   ├── scripts/data_prep/      # 数据预处理
│   └── scripts/data_analysis/  # 数据分析与建模
├── 表示层 (Presentation Layer)
│   ├── papers/drafts/      # 论文草稿
│   ├── papers/figures/     # 图表展示
│   └── papers/supplementary/ # 补充材料
└── 管理层 (Management Layer)
    └── memory-bank/        # 项目记忆与状态管理
```

### 2. 管道模式 (Pipeline Pattern)

**数据处理管道**：
```
原始数据 → 清洗 → 特征提取 → 模型训练 → 评估 → 解释
    ↓        ↓        ↓         ↓        ↓      ↓
  raw/  processed/ analysis_ready/ models/ results/ papers/
```

**质量检查点**：
- 每个阶段都有数据质量验证
- 自动生成处理日志和统计报告
- 支持断点续传和错误恢复

## 代码设计模式

### 1. 工厂模式 (Factory Pattern)

```python
class ModelFactory:
    """模型工厂，根据配置创建不同的模型"""
    
    @staticmethod
    def create_model(model_type, config):
        if model_type == "esm2_classifier":
            return ESM2AMPClassifier(**config)
        elif model_type == "traditional_ml":
            return TraditionalMLClassifier(**config)
        else:
            raise ValueError(f"Unknown model type: {model_type}")
```

### 2. 策略模式 (Strategy Pattern)

```python
class FeatureExtractionStrategy:
    """特征提取策略接口"""
    
    def extract(self, sequences):
        raise NotImplementedError

class ESM2Strategy(FeatureExtractionStrategy):
    """ESM-2特征提取策略"""
    
    def extract(self, sequences):
        # ESM-2特征提取实现
        pass

class TraditionalStrategy(FeatureExtractionStrategy):
    """传统特征提取策略"""
    
    def extract(self, sequences):
        # 传统特征提取实现
        pass
```

### 3. 观察者模式 (Observer Pattern)

```python
class TrainingMonitor:
    """训练监控器，观察训练过程"""
    
    def __init__(self):
        self.observers = []
    
    def add_observer(self, observer):
        self.observers.append(observer)
    
    def notify_epoch_end(self, epoch, metrics):
        for observer in self.observers:
            observer.on_epoch_end(epoch, metrics)

class WandBLogger:
    """Weights & Biases日志记录器"""
    
    def on_epoch_end(self, epoch, metrics):
        wandb.log(metrics, step=epoch)

class CheckpointSaver:
    """模型检查点保存器"""
    
    def on_epoch_end(self, epoch, metrics):
        if metrics['val_auroc'] > self.best_score:
            self.save_checkpoint(epoch, metrics)
```

## 数据管理模式

### 1. 版本控制模式

**文件命名约定**：
```
数据文件: <来源>_<类型>_<YYYYMMDD>.<扩展名>
模型文件: model_<架构>_<版本>_<YYYYMMDD_HHMMSS>.pth
结果文件: results_<实验名>_<YYYYMMDD_HHMMSS>.json
```

**版本追踪**：
- 每个数据文件包含SHA256哈希值
- 模型训练记录完整的超参数配置
- 实验结果包含可重现的随机种子

### 2. 缓存模式

```python
class FeatureCache:
    """特征缓存管理器"""
    
    def __init__(self, cache_dir):
        self.cache_dir = cache_dir
    
    def get_features(self, sequences, model_name):
        cache_key = self._generate_cache_key(sequences, model_name)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.npy")
        
        if os.path.exists(cache_file):
            return np.load(cache_file)
        
        # 计算特征并缓存
        features = self._extract_features(sequences, model_name)
        np.save(cache_file, features)
        return features
```

## 错误处理模式

### 1. 重试模式 (Retry Pattern)

```python
def retry_on_failure(max_retries=3, delay=1):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    time.sleep(delay * (2 ** attempt))  # 指数退避
            return None
        return wrapper
    return decorator

@retry_on_failure(max_retries=3)
def download_data(url):
    # 数据下载逻辑
    pass
```

### 2. 断路器模式 (Circuit Breaker Pattern)

```python
class CircuitBreaker:
    """断路器，防止级联失败"""
    
    def __init__(self, failure_threshold=5, timeout=60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func, *args, **kwargs):
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.timeout:
                self.state = 'HALF_OPEN'
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self.on_success()
            return result
        except Exception as e:
            self.on_failure()
            raise e
```

## 配置管理模式

### 1. 配置中心模式

```yaml
# config.yaml
project:
  name: "amp_classification"
  version: "1.0.0"

data:
  raw_dir: "data/raw"
  processed_dir: "data/processed"
  analysis_ready_dir: "data/analysis_ready"

model:
  esm2:
    model_name: "esm2_t33_650M_UR50D"
    batch_size: 16
    max_length: 200
  
  classifier:
    hidden_dims: [512, 256, 128]
    dropout: 0.3
    learning_rate: 1e-4

training:
  epochs: 50
  early_stopping_patience: 5
  validation_split: 0.2
```

### 2. 环境适配模式

```python
class EnvironmentConfig:
    """环境配置适配器"""
    
    def __init__(self):
        self.env = os.getenv('ENVIRONMENT', 'development')
        self.config = self._load_config()
    
    def _load_config(self):
        if self.env == 'development':
            return {
                'batch_size': 4,
                'model_name': 'esm2_t6_8M_UR50D',
                'debug': True
            }
        elif self.env == 'production':
            return {
                'batch_size': 16,
                'model_name': 'esm2_t33_650M_UR50D',
                'debug': False
            }
        elif self.env == 'cloud':
            return {
                'batch_size': 32,
                'model_name': 'esm2_t36_3B_UR50D',
                'use_distributed': True
            }
```

## 测试模式

### 1. 单元测试模式

```python
import unittest
from unittest.mock import Mock, patch

class TestSequenceCleaner(unittest.TestCase):
    
    def setUp(self):
        self.cleaner = SequenceCleaner(min_length=10, max_length=200)
    
    def test_clean_sequence_normal(self):
        """测试正常序列清洗"""
        input_seq = "ACDEFGHIKLMNPQRSTVWY"
        result = self.cleaner.clean_sequence(input_seq)
        self.assertEqual(result, input_seq)
    
    def test_clean_sequence_with_invalid_chars(self):
        """测试包含无效字符的序列"""
        input_seq = "ACDEFGHIKLMNPQRSTVWYXBZ"
        expected = "ACDEFGHIKLMNPQRSTVWYXNQ"
        result = self.cleaner.clean_sequence(input_seq)
        self.assertEqual(result, expected)
    
    @patch('requests.get')
    def test_download_with_mock(self, mock_get):
        """测试数据下载（使用Mock）"""
        mock_response = Mock()
        mock_response.text = ">seq1\nACDEFG\n"
        mock_get.return_value = mock_response
        
        # 测试下载逻辑
        pass
```

### 2. 集成测试模式

```python
class TestDataPipeline(unittest.TestCase):
    """数据处理管道集成测试"""
    
    def test_full_pipeline(self):
        """测试完整的数据处理流程"""
        # 1. 数据下载
        downloader = AMPDataDownloader(self.test_dir)
        success = downloader.download_test_data()
        self.assertTrue(success)
        
        # 2. 数据清洗
        cleaner = SequenceCleaner()
        cleaned_file = cleaner.clean_fasta_file(
            input_file=os.path.join(self.test_dir, 'test_raw.fasta'),
            output_file=os.path.join(self.test_dir, 'test_cleaned.fasta')
        )
        self.assertTrue(os.path.exists(cleaned_file))
        
        # 3. 特征提取
        extractor = ESM2FeatureExtractor('esm2_t6_8M_UR50D')
        features = extractor.extract_features(['ACDEFG', 'KLMNOP'])
        self.assertIsNotNone(features)
        self.assertEqual(features.shape[0], 2)
```

## 监控与日志模式

### 1. 结构化日志模式

```python
import logging
import json
from datetime import datetime

class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name):
        self.logger = logging.getLogger(name)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def log_event(self, event_type, **kwargs):
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'data': kwargs
        }
        self.logger.info(json.dumps(log_entry, ensure_ascii=False))
    
    def log_training_epoch(self, epoch, train_loss, val_loss, metrics):
        self.log_event('training_epoch',
                      epoch=epoch,
                      train_loss=train_loss,
                      val_loss=val_loss,
                      metrics=metrics)
```

### 2. 性能监控模式

```python
import time
import psutil
import torch
from contextlib import contextmanager

@contextmanager
def performance_monitor(operation_name):
    """性能监控上下文管理器"""
    start_time = time.time()
    start_memory = psutil.virtual_memory().used
    
    if torch.cuda.is_available():
        start_gpu_memory = torch.cuda.memory_allocated()
    
    try:
        yield
    finally:
        end_time = time.time()
        end_memory = psutil.virtual_memory().used
        
        metrics = {
            'operation': operation_name,
            'duration': end_time - start_time,
            'memory_delta': end_memory - start_memory
        }
        
        if torch.cuda.is_available():
            end_gpu_memory = torch.cuda.memory_allocated()
            metrics['gpu_memory_delta'] = end_gpu_memory - start_gpu_memory
        
        logger.log_event('performance_metrics', **metrics)

# 使用示例
with performance_monitor('feature_extraction'):
    features = extractor.extract_features(sequences)
```

## 最佳实践总结

### 1. 代码质量
- 遵循PEP 8编码规范
- 使用类型提示增强代码可读性
- 编写详细的中文文档字符串
- 保持函数单一职责原则

### 2. 数据管理
- 实施严格的数据版本控制
- 建立完整的数据血缘关系
- 定期备份关键数据和模型
- 使用校验和确保数据完整性

### 3. 实验管理
- 记录所有实验的完整配置
- 使用随机种子确保可重现性
- 建立标准化的评估指标
- 保存模型检查点和训练历史

### 4. 云服务器优化
- 使用混合精度训练节省显存
- 实施梯度累积处理大批次
- 定期清理临时文件和缓存
- 监控资源使用情况避免超额

### 5. 错误处理
- 实施优雅的错误恢复机制
- 提供详细的错误信息和建议
- 建立多层次的数据验证
- 使用断路器防止级联失败
