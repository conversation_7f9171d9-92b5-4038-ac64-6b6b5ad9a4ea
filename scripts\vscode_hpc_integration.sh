#!/bin/bash
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-27
# 描述: VSCode与高算平台集成脚本 - 直接申请CPU节点运行Jupyter

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

echo "🖥️  VSCode高算平台集成 - CPU节点Jupyter启动"
echo "平台地址: s20223050931@**************"
echo "=========================================="

# 检查是否在高算平台上
if [[ -z "$SLURM_JOB_ID" ]]; then
    print_info "当前不在SLURM作业中，将申请CPU节点"
    
    # 检查是否已经在登录节点
    if [[ $(hostname) == *"login"* ]] || [[ $(hostname) == *"head"* ]]; then
        print_info "检测到登录节点，准备申请计算节点"
    else
        print_warning "请确保您已连接到高算平台: s20223050931@**************"
    fi
    
    # 申请交互式CPU节点
    print_info "申请交互式CPU节点..."
    
    # 根据您的平台调整参数
    srun --partition=cpu \
         --cpus-per-task=16 \
         --mem=64G \
         --time=08:00:00 \
         --pty \
         bash -c "$(realpath $0) --in-compute-node"
    
    exit 0
fi

# 如果传入了 --in-compute-node 参数，说明已经在计算节点上
if [[ "$1" == "--in-compute-node" ]]; then
    print_success "已在计算节点上: $(hostname)"
    print_info "节点信息:"
    echo "  - 节点名: $SLURMD_NODENAME"
    echo "  - 作业ID: $SLURM_JOB_ID"
    echo "  - CPU核心: $SLURM_CPUS_PER_TASK"
    echo "  - 内存: ${SLURM_MEM_PER_NODE}MB"
    
    # 激活conda环境
    print_info "激活conda环境..."
    source ~/miniconda3/etc/profile.d/conda.sh || source ~/anaconda3/etc/profile.d/conda.sh
    conda activate amp_esm2
    
    # 设置环境变量
    export OMP_NUM_THREADS=$SLURM_CPUS_PER_TASK
    export MKL_NUM_THREADS=$SLURM_CPUS_PER_TASK
    export NUMEXPR_NUM_THREADS=$SLURM_CPUS_PER_TASK
    
    # 获取节点IP
    NODE_IP=$(hostname -i)
    PORT=8888
    
    # 检查端口是否被占用
    while netstat -an 2>/dev/null | grep -q ":$PORT "; do
        PORT=$((PORT + 1))
    done
    
    print_success "准备启动Jupyter Lab"
    echo "=========================================="
    echo "🌐 连接信息:"
    echo "  节点IP: $NODE_IP"
    echo "  端口: $PORT"
    echo "  完整地址: http://$NODE_IP:$PORT"
    echo ""
    echo "🔗 VSCode连接方式:"
    echo "  1. 在VSCode中按 Ctrl+Shift+P"
    echo "  2. 输入 'Jupyter: Specify Jupyter Server URI'"
    echo "  3. 输入: http://$NODE_IP:$PORT"
    echo ""
    echo "🚇 SSH隧道方式 (如果需要):"
    echo "  在本地终端运行:"
    echo "  ssh -L $PORT:$NODE_IP:$PORT s20223050931@**************"
    echo "  然后在VSCode中连接: http://localhost:$PORT"
    echo "=========================================="
    
    # 启动Jupyter Lab
    print_info "启动Jupyter Lab..."
    
    # 使用项目配置文件（如果存在）
    CONFIG_FILE="$(pwd)/configs/jupyter_config.py"
    
    if [ -f "$CONFIG_FILE" ]; then
        print_info "使用项目配置文件: $CONFIG_FILE"
        jupyter lab --config="$CONFIG_FILE" --port=$PORT
    else
        print_info "使用默认配置启动"
        jupyter lab \
            --ip=0.0.0.0 \
            --port=$PORT \
            --no-browser \
            --allow-root \
            --notebook-dir=$(pwd) \
            --ServerApp.token='' \
            --ServerApp.password='' \
            --ServerApp.allow_origin='*' \
            --ServerApp.disable_check_xsrf=True \
            --ServerApp.allow_remote_access=True \
            --ServerApp.max_buffer_size=1073741824 \
            --ServerApp.iopub_data_rate_limit=100000000
    fi
    
else
    print_error "脚本执行错误，请检查参数"
    exit 1
fi
