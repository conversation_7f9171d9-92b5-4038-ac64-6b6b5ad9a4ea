# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-07-20
# 描述: 分析抗菌肽数据集的类别构成，解决混合标签问题

import os
import pandas as pd
import numpy as np
from Bio import SeqIO
from datetime import datetime
import json
import re
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns

class AMPCategoryAnalyzer:
    """抗菌肽类别分析器"""
    
    def __init__(self, data_dir):
        self.data_dir = data_dir
        self.category_data = {}
        
        # 定义AMP类别映射
        self.category_mapping = {
            # 抗细菌类
            'antibacterial': {
                'keywords': ['antibacterial', 'bacterial'],
                'description': '抗细菌肽',
                'target': 'bacteria',
                'priority': 'high'
            },
            # 抗真菌类
            'antifungal': {
                'keywords': ['antifungal', 'fungal'],
                'description': '抗真菌肽',
                'target': 'fungi',
                'priority': 'medium'
            },
            # 抗病毒类
            'antiviral': {
                'keywords': ['antiviral', 'viral'],
                'description': '抗病毒肽',
                'target': 'virus',
                'priority': 'medium'
            },
            # 通用抗菌类
            'antimicrobial': {
                'keywords': ['antimicrobial', 'general'],
                'description': '通用抗菌肽',
                'target': 'mixed',
                'priority': 'low'
            },
            # 天然/合成分类
            'natural': {
                'keywords': ['natural'],
                'description': '天然抗菌肽',
                'target': 'mixed',
                'priority': 'info'
            },
            'synthetic': {
                'keywords': ['synthetic'],
                'description': '合成抗菌肽',
                'target': 'mixed',
                'priority': 'info'
            }
        }
    
    def categorize_file(self, filename):
        """根据文件名确定AMP类别"""
        filename_lower = filename.lower()
        categories = []
        
        for category, info in self.category_mapping.items():
            for keyword in info['keywords']:
                if keyword in filename_lower:
                    categories.append(category)
                    break
        
        return categories if categories else ['unknown']
    
    def analyze_file_content(self, filepath, filename):
        """分析单个文件的内容"""
        try:
            sequences = []
            sequence_lengths = []
            
            # 读取序列
            for record in SeqIO.parse(filepath, "fasta"):
                seq_str = str(record.seq).upper()
                sequences.append(seq_str)
                sequence_lengths.append(len(seq_str))
            
            # 确定类别
            categories = self.categorize_file(filename)
            
            # 计算统计信息
            stats = {
                'filename': filename,
                'categories': categories,
                'primary_category': categories[0] if categories else 'unknown',
                'total_sequences': len(sequences),
                'mean_length': np.mean(sequence_lengths) if sequence_lengths else 0,
                'median_length': np.median(sequence_lengths) if sequence_lengths else 0,
                'sequences_10_100': sum(1 for l in sequence_lengths if 10 <= l <= 100),
                'training_suitable_ratio': sum(1 for l in sequence_lengths if 10 <= l <= 100) / len(sequences) if sequences else 0
            }
            
            return stats
            
        except Exception as e:
            print(f"❌ 分析文件 {filename} 失败: {e}")
            return None
    
    def analyze_all_files(self):
        """分析所有FASTA文件"""
        print("🔍 分析抗菌肽数据集的类别构成...")
        print("=" * 60)
        
        fasta_files = [f for f in os.listdir(self.data_dir) if f.endswith('.fasta')]
        
        for filename in sorted(fasta_files):
            filepath = os.path.join(self.data_dir, filename)
            print(f"\n📁 分析文件: {filename}")
            
            stats = self.analyze_file_content(filepath, filename)
            
            if stats:
                self.category_data[filename] = stats
                print(f"   类别: {', '.join(stats['categories'])}")
                print(f"   序列数: {stats['total_sequences']:,}")
                print(f"   适合训练: {stats['sequences_10_100']:,} ({stats['training_suitable_ratio']*100:.1f}%)")
    
    def generate_category_report(self):
        """生成类别分析报告"""
        if not self.category_data:
            print("❌ 没有可分析的数据")
            return
        
        print("\n" + "=" * 70)
        print("📊 抗菌肽类别构成分析报告")
        print("=" * 70)
        
        # 按类别统计
        category_stats = {}
        for filename, data in self.category_data.items():
            primary_cat = data['primary_category']
            if primary_cat not in category_stats:
                category_stats[primary_cat] = {
                    'files': [],
                    'total_sequences': 0,
                    'training_suitable': 0
                }
            
            category_stats[primary_cat]['files'].append(filename)
            category_stats[primary_cat]['total_sequences'] += data['total_sequences']
            category_stats[primary_cat]['training_suitable'] += data['sequences_10_100']
        
        # 显示类别统计
        print(f"📋 按类别统计:")
        print(f"{'类别':<15} {'文件数':<8} {'总序列数':<10} {'适合训练':<10} {'描述':<20}")
        print("-" * 75)
        
        for category, stats in sorted(category_stats.items()):
            description = self.category_mapping.get(category, {}).get('description', '未知类别')
            print(f"{category:<15} {len(stats['files']):<8} {stats['total_sequences']:<10,} {stats['training_suitable']:<10,} {description:<20}")
        
        # 问题分析
        print(f"\n⚠️ 混合标签问题分析:")
        
        # 检查是否有混合类别
        mixed_categories = [cat for cat in category_stats.keys() 
                           if self.category_mapping.get(cat, {}).get('target') == 'mixed']
        specific_categories = [cat for cat in category_stats.keys() 
                              if self.category_mapping.get(cat, {}).get('target') not in ['mixed', None]]
        
        if len(specific_categories) > 1:
            print(f"   ❌ 发现多种特异性类别: {', '.join(specific_categories)}")
            print(f"   ❌ 这些类别有不同的生物学机制，不应混合训练")
        
        if mixed_categories:
            print(f"   ⚠️ 发现混合类别: {', '.join(mixed_categories)}")
            print(f"   ⚠️ 这些类别包含多种靶标，可能影响模型性能")
        
        # 建议的解决方案
        print(f"\n💡 建议的解决方案:")
        
        # 方案1：专注抗细菌
        if 'antibacterial' in category_stats:
            antibacterial_count = category_stats['antibacterial']['training_suitable']
            print(f"   ✅ 方案1 - 专注抗细菌肽:")
            print(f"      - 使用 antibacterial 类别: {antibacterial_count:,} 个适合训练的序列")
            print(f"      - 优点: 生物学机制明确，模型性能更好")
            print(f"      - 缺点: 数据量相对较少")
        
        # 方案2：分别建模
        if len(specific_categories) > 1:
            print(f"   ✅ 方案2 - 分别建立专门模型:")
            for cat in specific_categories:
                count = category_stats[cat]['training_suitable']
                target = self.category_mapping.get(cat, {}).get('target', 'unknown')
                print(f"      - {cat} 模型 (针对{target}): {count:,} 个序列")
        
        # 方案3：层次化建模
        total_specific = sum(category_stats[cat]['training_suitable'] 
                           for cat in specific_categories)
        if total_specific > 20000:
            print(f"   ✅ 方案3 - 层次化多分类模型:")
            print(f"      - 第一层: AMP vs 非AMP")
            print(f"      - 第二层: 抗细菌 vs 抗真菌 vs 抗病毒")
            print(f"      - 总数据量: {total_specific:,} 个序列")
        
        return category_stats
    
    def recommend_best_strategy(self, category_stats):
        """推荐最佳策略"""
        print(f"\n🎯 推荐策略:")
        
        # 检查抗细菌数据量
        antibacterial_count = category_stats.get('antibacterial', {}).get('training_suitable', 0)
        
        if antibacterial_count >= 15000:
            print(f"   🏆 推荐: 专注抗细菌肽分类")
            print(f"   理由:")
            print(f"   - 数据量充足: {antibacterial_count:,} 个序列")
            print(f"   - 生物学机制明确")
            print(f"   - 临床应用价值高")
            print(f"   - 符合学术研究标准")
            
            strategy = 'antibacterial_focus'
        elif sum(category_stats[cat]['training_suitable'] for cat in category_stats) >= 30000:
            print(f"   🏆 推荐: 层次化多分类模型")
            print(f"   理由:")
            print(f"   - 总数据量充足")
            print(f"   - 可以充分利用所有数据")
            print(f"   - 提供更全面的预测能力")
            
            strategy = 'hierarchical_multi'
        else:
            print(f"   🏆 推荐: 补充数据后再决定")
            print(f"   理由:")
            print(f"   - 当前数据量可能不足")
            print(f"   - 建议获取APD和CAMPR4数据")
            
            strategy = 'need_more_data'
        
        return strategy
    
    def create_filtered_datasets(self, strategy='antibacterial_focus'):
        """根据策略创建筛选后的数据集"""
        print(f"\n🔧 根据策略创建筛选数据集...")
        
        if strategy == 'antibacterial_focus':
            # 只保留抗细菌肽
            antibacterial_files = [filename for filename, data in self.category_data.items() 
                                 if 'antibacterial' in data['categories']]
            
            print(f"   选择抗细菌肽文件: {len(antibacterial_files)} 个")
            for filename in antibacterial_files:
                print(f"   - {filename}")
            
            return antibacterial_files
        
        elif strategy == 'hierarchical_multi':
            # 按类别分组
            category_files = {}
            for filename, data in self.category_data.items():
                primary_cat = data['primary_category']
                if primary_cat not in category_files:
                    category_files[primary_cat] = []
                category_files[primary_cat].append(filename)
            
            print(f"   按类别分组:")
            for category, files in category_files.items():
                print(f"   - {category}: {len(files)} 个文件")
            
            return category_files
        
        return None
    
    def save_analysis_results(self, category_stats, strategy):
        """保存分析结果"""
        output_file = os.path.join(self.data_dir, f'category_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        analysis_data = {
            'analysis_timestamp': datetime.now().isoformat(),
            'category_statistics': category_stats,
            'recommended_strategy': strategy,
            'file_details': self.category_data,
            'category_mapping': self.category_mapping
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 类别分析结果已保存到: {output_file}")

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    data_dir = os.path.join(project_root, 'data', 'raw')
    
    # 创建分析器
    analyzer = AMPCategoryAnalyzer(data_dir)
    
    # 分析所有文件
    analyzer.analyze_all_files()
    
    # 生成报告
    category_stats = analyzer.generate_category_report()
    
    # 推荐策略
    strategy = analyzer.recommend_best_strategy(category_stats)
    
    # 创建筛选数据集
    filtered_datasets = analyzer.create_filtered_datasets(strategy)
    
    # 保存结果
    analyzer.save_analysis_results(category_stats, strategy)
    
    print(f"\n🎉 类别分析完成！")
    print(f"📝 下一步: 根据推荐策略重新组织训练数据")

if __name__ == "__main__":
    main()
