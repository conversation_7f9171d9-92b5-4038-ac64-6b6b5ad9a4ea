---
type: "agent_requested"
---

# 规则：从本地开发到高算训练的完整工作流程

本规则旨在固化一个标准化的开发流程，涵盖从本地Jupyter Notebook原型开发，到代码脚本化，再到最终在高性能计算（HPC）集群上进行大规模模型训练的全过程。

## 核心原则

1.  **分阶段开发**: 严格区分本地调试与高算训练，本地只使用少量数据进行逻辑验证，高算负责全量数据和生产级训练。
2.  **代码模块化**: 禁止在最终的训练脚本中使用Jupyter Notebook。所有代码必须整理成模块化的Python脚本（.py），以提高代码的可复用性、可维护性和稳定性。
3.  **配置驱动**: 所有实验参数（如路径、超参数）必须通过配置文件（如`config.yaml`）管理，训练脚本通过读取配置文件来执行。这确保了实验的可复现性。
4.  **环境一致性**: 本地开发环境（Conda）和高算环境必须严格保持一致，依赖通过 `requirements.txt` 文件管理。
5.  **自动化至上**: 高算上的训练任务必须通过作业脚本（如SLURM的.sh）提交，实现自动化调度和日志记录。

## 工作流程详解

### 阶段一：本地原型开发与调试 (Jupyter)

1.  **环境设置**:
    *   在本地使用Conda创建与高算一致的虚拟环境。
    *   安装CPU版本的PyTorch和其他核心依赖，以加速本地调试。
2.  **原型验证**:
    *   使用Jupyter Notebook (`.ipynb`) 进行快速原型设计和算法验证。
    *   **必须**使用一小部分采样数据（如100条）来测试数据加载、模型结构和训练循环。目标是验证代码逻辑的正确性，而非模型性能。

### 阶段二：代码脚本化与项目构建

1.  **项目结构**: 将验证通过的Notebook代码，按照标准化的项目结构整理成独立的Python脚本。
    ```
    amp_project/
    ├── src/
    │   ├── dataset.py       # 数据集类 (e.g., AMPDataset)
    │   ├── model.py         # 模型定义 (e.g., ESM2AMPClassifier)
    │   ├── train.py         # 主训练脚本 (使用PyTorch Lightning)
    │   └── utils.py         # 工具函数
    ├── configs/
    │   └── config.yaml      # 配置文件
    ├── scripts/
    │   └── run_training.sh  # 高算作业脚本 (SLURM/PBS)
    ├── requirements.txt     # Python依赖
    └── README.md
    ```
2.  **代码转换**:
    *   `dataset.py`: 包含PyTorch `Dataset` 类，负责数据的加载和预处理。
    *   `model.py`: 包含`torch.nn.Module` 或 `pl.LightningModule` 中的模型定义。大型预训练模型（如ESM-2）的加载应放在`setup`阶段，避免在本地初始化时消耗资源。
    *   `train.py`: 核心训练逻辑，使用`argparse`接收配置文件路径。推荐使用PyTorch Lightning以简化训练流程，并内置混合精度、回调（Checkpoints, EarlyStopping）等功能。
3.  **配置文件 (`config.yaml`)**:
    *   将所有可变参数（数据路径、模型参数、训练参数、优化器设置）集中管理。路径应使用高算集群上的绝对路径。

### 阶段三：高算环境准备与部署

1.  **代码同步**:
    *   使用 `rsync`, `git` 或 `scp` 将结构化的项目代码上传到HPC的用户目录下。`rsync` 是推荐方式，可以方便地排除不必要的文件（如`.ipynb`, `__pycache__`）。
2.  **环境搭建**:
    *   在HPC上使用`conda`和`requirements.txt`文件，精确复制本地的开发环境。
    *   如有需要，预先下载大型模型文件到指定缓存目录，避免在任务运行时因网络问题失败。
3.  **作业脚本 (`run_training.sh`)**:
    *   根据HPC的作业调度系统（SLURM或PBS）编写脚本。
    *   脚本内必须包含：资源申请（GPU, CPU, 内存, 时间）、模块加载、Conda环境激活、必要的目录创建（如`checkpoints`, `logs`）、以及启动`train.py`的命令。
    *   推荐在训练前后加入评估或后处理步骤。

### 阶段四：任务提交、监控与结果回收

1.  **提交任务**: 使用 `sbatch run_training.sh` (SLURM) 或 `qsub run_training.sh` (PBS) 提交训练作业。
2.  **状态监控**:
    *   **基础监控**: 使用 `tail -f` 查看实时日志，`watch nvidia-smi` 监控GPU状态。
    *   **高级监控 (推荐)**: 在`train.py`中集成`WandbLogger`或`TensorBoardLogger`，通过Web界面远程、实时、可视化地监控训练过程（损失、指标、GPU利用率等）。
3.  **结果回收**:
    *   训练完成后，使用`rsync`或`scp`将产出的结果（模型检查点、日志文件、评估结果）下载到本地。
4.  **本地分析**:
    *   回到Jupyter Notebook环境，加载下载的模型和日志数据，进行深入的性能分析、误差分析和可视化。

## 实用技巧集成

*   **调试模式**: 在`train.py`中加入`--debug`标志，当激活时，自动使用少量数据并减少训练轮次。
*   **断点续训**: 利用PyTorch Lightning的`resume_from_checkpoint`参数，使训练任务可以从上次中断的地方恢复。
*   **多GPU训练**: 作业脚本中申请多个GPU，并在`pl.Trainer`中设置好多卡训练策略（如`strategy='ddp'`）。

